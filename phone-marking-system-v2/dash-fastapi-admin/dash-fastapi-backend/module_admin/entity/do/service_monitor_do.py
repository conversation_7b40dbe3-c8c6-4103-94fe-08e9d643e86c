"""
服务监控相关数据模型
用于存储核心服务的监控数据、健康检查记录、性能指标等信息
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, BigInteger, JSON
from config.database import Base
from datetime import datetime


class ServiceInfo(Base):
    """
    核心服务信息表
    存储系统中所有需要监控的核心服务基本信息
    """
    __tablename__ = 'service_info'

    service_id = Column(Integer, primary_key=True, autoincrement=True, comment='服务ID')
    service_name = Column(String(50), nullable=False, unique=True, comment='服务名称')
    service_type = Column(String(20), nullable=False, comment='服务类型(system/application)')
    display_name = Column(String(100), nullable=False, comment='显示名称')
    description = Column(String(500), comment='服务描述')
    host = Column(String(100), default='127.0.0.1', comment='服务主机')
    port = Column(Integer, comment='服务端口')
    health_check_url = Column(String(200), comment='健康检查URL')
    health_check_interval = Column(Integer, default=30, comment='健康检查间隔(秒)')
    is_critical = Column(Boolean, default=True, comment='是否为关键服务')
    auto_restart = Column(Boolean, default=True, comment='是否自动重启')
    max_restart_attempts = Column(Integer, default=3, comment='最大重启尝试次数')
    dependencies = Column(JSON, comment='服务依赖关系')
    config_params = Column(JSON, comment='服务配置参数')
    status = Column(String(20), default='active', comment='服务状态(active/inactive)')
    created_by = Column(String(64), default='system', comment='创建者')
    created_time = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_by = Column(String(64), default='system', comment='更新者')
    updated_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')


class ServiceStatus(Base):
    """
    服务状态记录表
    实时记录各个服务的运行状态
    """
    __tablename__ = 'service_status'

    status_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='状态记录ID')
    service_id = Column(Integer, nullable=False, comment='服务ID')
    service_name = Column(String(50), nullable=False, comment='服务名称')
    status = Column(String(20), nullable=False, comment='服务状态(running/stopped/error/starting/stopping)')
    health_status = Column(String(20), comment='健康状态(healthy/unhealthy/unknown)')
    pid = Column(Integer, comment='进程ID')
    cpu_usage = Column(Float, comment='CPU使用率(%)')
    memory_usage = Column(Float, comment='内存使用率(%)')
    memory_used_mb = Column(Float, comment='内存使用量(MB)')
    disk_usage = Column(Float, comment='磁盘使用率(%)')
    network_connections = Column(Integer, comment='网络连接数')
    uptime_seconds = Column(BigInteger, comment='运行时间(秒)')
    response_time_ms = Column(Float, comment='响应时间(毫秒)')
    error_message = Column(Text, comment='错误信息')
    last_check_time = Column(DateTime, default=datetime.now, comment='最后检查时间')
    created_time = Column(DateTime, default=datetime.now, comment='记录创建时间')


class ServiceHealthCheck(Base):
    """
    服务健康检查记录表
    记录每次健康检查的详细结果
    """
    __tablename__ = 'service_health_check'

    check_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='检查记录ID')
    service_id = Column(Integer, nullable=False, comment='服务ID')
    service_name = Column(String(50), nullable=False, comment='服务名称')
    check_type = Column(String(20), nullable=False, comment='检查类型(process/port/http/custom)')
    check_result = Column(String(20), nullable=False, comment='检查结果(success/failed/timeout)')
    response_time_ms = Column(Float, comment='响应时间(毫秒)')
    status_code = Column(Integer, comment='HTTP状态码')
    response_body = Column(Text, comment='响应内容')
    error_message = Column(Text, comment='错误信息')
    check_details = Column(JSON, comment='检查详细信息')
    check_time = Column(DateTime, default=datetime.now, comment='检查时间')


class ServicePerformanceMetrics(Base):
    """
    服务性能指标表
    记录服务的各项性能指标数据
    """
    __tablename__ = 'service_performance_metrics'

    metric_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='指标记录ID')
    service_id = Column(Integer, nullable=False, comment='服务ID')
    service_name = Column(String(50), nullable=False, comment='服务名称')
    metric_name = Column(String(100), nullable=False, comment='指标名称')
    metric_type = Column(String(20), nullable=False, comment='指标类型(counter/gauge/histogram)')
    metric_value = Column(Float, nullable=False, comment='指标值')
    metric_unit = Column(String(20), comment='指标单位')
    labels = Column(JSON, comment='指标标签')
    timestamp = Column(DateTime, default=datetime.now, comment='指标时间戳')
    created_time = Column(DateTime, default=datetime.now, comment='记录创建时间')


class ServiceAlert(Base):
    """
    服务告警记录表
    记录服务监控过程中产生的各种告警信息
    """
    __tablename__ = 'service_alert'

    alert_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='告警ID')
    service_id = Column(Integer, nullable=False, comment='服务ID')
    service_name = Column(String(50), nullable=False, comment='服务名称')
    alert_type = Column(String(50), nullable=False, comment='告警类型')
    alert_level = Column(String(20), nullable=False, comment='告警级别(info/warning/error/critical)')
    alert_title = Column(String(200), nullable=False, comment='告警标题')
    alert_message = Column(Text, nullable=False, comment='告警消息')
    alert_details = Column(JSON, comment='告警详细信息')
    threshold_value = Column(Float, comment='阈值')
    current_value = Column(Float, comment='当前值')
    is_resolved = Column(Boolean, default=False, comment='是否已解决')
    resolved_time = Column(DateTime, comment='解决时间')
    resolved_by = Column(String(64), comment='解决人')
    resolution_notes = Column(Text, comment='解决备注')
    alert_time = Column(DateTime, default=datetime.now, comment='告警时间')
    created_time = Column(DateTime, default=datetime.now, comment='记录创建时间')


class ServiceLog(Base):
    """
    服务日志记录表
    存储服务运行过程中的重要日志信息
    """
    __tablename__ = 'service_log'

    log_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='日志ID')
    service_id = Column(Integer, nullable=False, comment='服务ID')
    service_name = Column(String(50), nullable=False, comment='服务名称')
    log_level = Column(String(20), nullable=False, comment='日志级别(DEBUG/INFO/WARNING/ERROR/CRITICAL)')
    log_source = Column(String(100), comment='日志来源')
    log_message = Column(Text, nullable=False, comment='日志消息')
    log_details = Column(JSON, comment='日志详细信息')
    exception_info = Column(Text, comment='异常信息')
    request_id = Column(String(100), comment='请求ID')
    user_id = Column(String(64), comment='用户ID')
    ip_address = Column(String(50), comment='IP地址')
    log_time = Column(DateTime, default=datetime.now, comment='日志时间')
    created_time = Column(DateTime, default=datetime.now, comment='记录创建时间')


class ServiceOperation(Base):
    """
    服务操作记录表
    记录对服务进行的各种运维操作
    """
    __tablename__ = 'service_operation'

    operation_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='操作ID')
    service_id = Column(Integer, nullable=False, comment='服务ID')
    service_name = Column(String(50), nullable=False, comment='服务名称')
    operation_type = Column(String(50), nullable=False, comment='操作类型(start/stop/restart/config/deploy)')
    operation_status = Column(String(20), nullable=False, comment='操作状态(pending/running/success/failed)')
    operation_params = Column(JSON, comment='操作参数')
    operation_result = Column(Text, comment='操作结果')
    error_message = Column(Text, comment='错误信息')
    execution_time_ms = Column(Integer, comment='执行时间(毫秒)')
    operator = Column(String(64), nullable=False, comment='操作人')
    operator_ip = Column(String(50), comment='操作人IP')
    operation_time = Column(DateTime, default=datetime.now, comment='操作时间')
    completed_time = Column(DateTime, comment='完成时间')
    created_time = Column(DateTime, default=datetime.now, comment='记录创建时间')
