"""
服务监控相关视图对象模型
用于API请求和响应的数据传输
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime


class ServiceInfoModel(BaseModel):
    """服务信息模型"""
    service_id: Optional[int] = Field(None, description="服务ID")
    service_name: str = Field(..., description="服务名称")
    service_type: str = Field(..., description="服务类型")
    display_name: str = Field(..., description="显示名称")
    description: Optional[str] = Field(None, description="服务描述")
    host: str = Field(default="127.0.0.1", description="服务主机")
    port: Optional[int] = Field(None, description="服务端口")
    health_check_url: Optional[str] = Field(None, description="健康检查URL")
    health_check_interval: int = Field(default=30, description="健康检查间隔(秒)")
    is_critical: bool = Field(default=True, description="是否为关键服务")
    auto_restart: bool = Field(default=True, description="是否自动重启")
    max_restart_attempts: int = Field(default=3, description="最大重启尝试次数")
    dependencies: Optional[List[str]] = Field(None, description="服务依赖关系")
    config_params: Optional[Dict[str, Any]] = Field(None, description="服务配置参数")
    status: str = Field(default="active", description="服务状态")


class ServiceStatusModel(BaseModel):
    """服务状态模型"""
    status_id: Optional[int] = Field(None, description="状态记录ID")
    service_id: int = Field(..., description="服务ID")
    service_name: str = Field(..., description="服务名称")
    status: str = Field(..., description="服务状态")
    health_status: Optional[str] = Field(None, description="健康状态")
    pid: Optional[int] = Field(None, description="进程ID")
    cpu_usage: Optional[float] = Field(None, description="CPU使用率(%)")
    memory_usage: Optional[float] = Field(None, description="内存使用率(%)")
    memory_used_mb: Optional[float] = Field(None, description="内存使用量(MB)")
    disk_usage: Optional[float] = Field(None, description="磁盘使用率(%)")
    network_connections: Optional[int] = Field(None, description="网络连接数")
    uptime_seconds: Optional[int] = Field(None, description="运行时间(秒)")
    response_time_ms: Optional[float] = Field(None, description="响应时间(毫秒)")
    error_message: Optional[str] = Field(None, description="错误信息")
    last_check_time: Optional[datetime] = Field(None, description="最后检查时间")


class ServiceHealthCheckModel(BaseModel):
    """服务健康检查模型"""
    check_id: Optional[int] = Field(None, description="检查记录ID")
    service_id: int = Field(..., description="服务ID")
    service_name: str = Field(..., description="服务名称")
    check_type: str = Field(..., description="检查类型")
    check_result: str = Field(..., description="检查结果")
    response_time_ms: Optional[float] = Field(None, description="响应时间(毫秒)")
    status_code: Optional[int] = Field(None, description="HTTP状态码")
    response_body: Optional[str] = Field(None, description="响应内容")
    error_message: Optional[str] = Field(None, description="错误信息")
    check_details: Optional[Dict[str, Any]] = Field(None, description="检查详细信息")
    check_time: Optional[datetime] = Field(None, description="检查时间")


class ServicePerformanceMetricsModel(BaseModel):
    """服务性能指标模型"""
    metric_id: Optional[int] = Field(None, description="指标记录ID")
    service_id: int = Field(..., description="服务ID")
    service_name: str = Field(..., description="服务名称")
    metric_name: str = Field(..., description="指标名称")
    metric_type: str = Field(..., description="指标类型")
    metric_value: float = Field(..., description="指标值")
    metric_unit: Optional[str] = Field(None, description="指标单位")
    labels: Optional[Dict[str, str]] = Field(None, description="指标标签")
    timestamp: Optional[datetime] = Field(None, description="指标时间戳")


class ServiceAlertModel(BaseModel):
    """服务告警模型"""
    alert_id: Optional[int] = Field(None, description="告警ID")
    service_id: int = Field(..., description="服务ID")
    service_name: str = Field(..., description="服务名称")
    alert_type: str = Field(..., description="告警类型")
    alert_level: str = Field(..., description="告警级别")
    alert_title: str = Field(..., description="告警标题")
    alert_message: str = Field(..., description="告警消息")
    alert_details: Optional[Dict[str, Any]] = Field(None, description="告警详细信息")
    threshold_value: Optional[float] = Field(None, description="阈值")
    current_value: Optional[float] = Field(None, description="当前值")
    is_resolved: bool = Field(default=False, description="是否已解决")
    resolved_time: Optional[datetime] = Field(None, description="解决时间")
    resolved_by: Optional[str] = Field(None, description="解决人")
    resolution_notes: Optional[str] = Field(None, description="解决备注")
    alert_time: Optional[datetime] = Field(None, description="告警时间")


class ServiceLogModel(BaseModel):
    """服务日志模型"""
    log_id: Optional[int] = Field(None, description="日志ID")
    service_id: int = Field(..., description="服务ID")
    service_name: str = Field(..., description="服务名称")
    log_level: str = Field(..., description="日志级别")
    log_source: Optional[str] = Field(None, description="日志来源")
    log_message: str = Field(..., description="日志消息")
    log_details: Optional[Dict[str, Any]] = Field(None, description="日志详细信息")
    exception_info: Optional[str] = Field(None, description="异常信息")
    request_id: Optional[str] = Field(None, description="请求ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    ip_address: Optional[str] = Field(None, description="IP地址")
    log_time: Optional[datetime] = Field(None, description="日志时间")


class ServiceOperationModel(BaseModel):
    """服务操作模型"""
    operation_id: Optional[int] = Field(None, description="操作ID")
    service_id: int = Field(..., description="服务ID")
    service_name: str = Field(..., description="服务名称")
    operation_type: str = Field(..., description="操作类型")
    operation_status: str = Field(..., description="操作状态")
    operation_params: Optional[Dict[str, Any]] = Field(None, description="操作参数")
    operation_result: Optional[str] = Field(None, description="操作结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    execution_time_ms: Optional[int] = Field(None, description="执行时间(毫秒)")
    operator: str = Field(..., description="操作人")
    operator_ip: Optional[str] = Field(None, description="操作人IP")
    operation_time: Optional[datetime] = Field(None, description="操作时间")
    completed_time: Optional[datetime] = Field(None, description="完成时间")


class ServiceQueryModel(BaseModel):
    """服务查询模型"""
    service_name: Optional[str] = Field(None, description="服务名称")
    service_type: Optional[str] = Field(None, description="服务类型")
    status: Optional[str] = Field(None, description="服务状态")
    is_critical: Optional[bool] = Field(None, description="是否为关键服务")
    page_num: int = Field(default=1, description="页码")
    page_size: int = Field(default=10, description="每页大小")


class ServiceDashboardModel(BaseModel):
    """服务监控仪表板模型"""
    total_services: int = Field(..., description="总服务数")
    running_services: int = Field(..., description="运行中服务数")
    stopped_services: int = Field(..., description="停止服务数")
    error_services: int = Field(..., description="异常服务数")
    critical_services: int = Field(..., description="关键服务数")
    healthy_services: int = Field(..., description="健康服务数")
    unhealthy_services: int = Field(..., description="不健康服务数")
    avg_cpu_usage: float = Field(..., description="平均CPU使用率")
    avg_memory_usage: float = Field(..., description="平均内存使用率")
    total_alerts: int = Field(..., description="总告警数")
    unresolved_alerts: int = Field(..., description="未解决告警数")
    recent_operations: List[ServiceOperationModel] = Field(..., description="最近操作")


class ServicePerformanceChartModel(BaseModel):
    """服务性能图表模型"""
    service_name: str = Field(..., description="服务名称")
    time_range: str = Field(..., description="时间范围")
    cpu_data: List[Dict[str, Union[str, float]]] = Field(..., description="CPU数据")
    memory_data: List[Dict[str, Union[str, float]]] = Field(..., description="内存数据")
    response_time_data: List[Dict[str, Union[str, float]]] = Field(..., description="响应时间数据")
    network_data: List[Dict[str, Union[str, int]]] = Field(..., description="网络连接数据")


class ServiceLogQueryModel(BaseModel):
    """服务日志查询模型"""
    service_name: Optional[str] = Field(None, description="服务名称")
    log_level: Optional[str] = Field(None, description="日志级别")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    keyword: Optional[str] = Field(None, description="关键词")
    page_num: int = Field(default=1, description="页码")
    page_size: int = Field(default=50, description="每页大小")


class ServiceAlertQueryModel(BaseModel):
    """服务告警查询模型"""
    service_name: Optional[str] = Field(None, description="服务名称")
    alert_level: Optional[str] = Field(None, description="告警级别")
    alert_type: Optional[str] = Field(None, description="告警类型")
    is_resolved: Optional[bool] = Field(None, description="是否已解决")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    page_num: int = Field(default=1, description="页码")
    page_size: int = Field(default=20, description="每页大小")


class ServiceOperationQueryModel(BaseModel):
    """服务操作查询模型"""
    service_name: Optional[str] = Field(None, description="服务名称")
    operation_type: Optional[str] = Field(None, description="操作类型")
    operation_status: Optional[str] = Field(None, description="操作状态")
    operator: Optional[str] = Field(None, description="操作人")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    page_num: int = Field(default=1, description="页码")
    page_size: int = Field(default=20, description="每页大小")
