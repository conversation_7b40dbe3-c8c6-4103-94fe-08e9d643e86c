"""
服务监控业务逻辑层
提供服务监控相关的业务处理方法
"""
import psutil
import requests
import subprocess
import socket
import time
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from module_admin.dao.service_monitor_dao import ServiceMonitorDao
from module_admin.entity.vo.service_monitor_vo import (
    ServiceInfoModel, ServiceStatusModel, ServiceHealthCheckModel,
    ServicePerformanceMetricsModel, ServiceAlertModel, ServiceLogModel,
    ServiceOperationModel, ServiceDashboardModel, ServicePerformanceChartModel,
    ServiceQueryModel, ServiceLogQueryModel, ServiceAlertQueryModel,
    ServiceOperationQueryModel
)
from utils.response_util import response_200, response_400, response_500
from utils.log_util import logger


class ServiceMonitorService:
    """服务监控业务服务类"""

    # 核心服务配置
    CORE_SERVICES = {
        'mysql': {
            'display_name': 'MySQL数据库',
            'service_type': 'system',
            'port': 3306,
            'health_check_type': 'port',
            'is_critical': True,
            'description': 'MySQL数据库服务'
        },
        'redis': {
            'display_name': 'Redis缓存',
            'service_type': 'system',
            'port': 6379,
            'health_check_type': 'port',
            'is_critical': True,
            'description': 'Redis缓存服务'
        },
        'backend': {
            'display_name': '后端API服务',
            'service_type': 'application',
            'port': 9099,
            'health_check_type': 'http',
            'health_check_url': 'http://127.0.0.1:9099/health',
            'is_critical': True,
            'description': 'FastAPI后端服务'
        },
        'frontend': {
            'display_name': '前端Web服务',
            'service_type': 'application',
            'port': 8089,
            'health_check_type': 'http',
            'health_check_url': 'http://127.0.0.1:8089/',
            'is_critical': True,
            'description': 'Dash前端服务'
        },
        'location': {
            'display_name': '归属地服务',
            'service_type': 'application',
            'port': 8001,
            'health_check_type': 'http',
            'health_check_url': 'http://127.0.0.1:8001/health',
            'is_critical': False,
            'description': '电话号码归属地查询服务'
        },
        'nlp': {
            'display_name': 'NLP处理服务',
            'service_type': 'application',
            'port': 8002,
            'health_check_type': 'http',
            'health_check_url': 'http://127.0.0.1:8002/health',
            'is_critical': False,
            'description': '自然语言处理服务'
        },
        'batch': {
            'display_name': '批处理服务',
            'service_type': 'application',
            'port': 8003,
            'health_check_type': 'http',
            'health_check_url': 'http://127.0.0.1:8003/health',
            'is_critical': False,
            'description': '批量数据处理服务'
        },
        'gateway': {
            'display_name': 'API网关',
            'service_type': 'application',
            'port': 8000,
            'health_check_type': 'http',
            'health_check_url': 'http://127.0.0.1:8000/health',
            'is_critical': True,
            'description': 'API网关服务'
        }
    }

    @staticmethod
    def initialize_core_services(db: Session) -> ResponseUtil:
        """
        初始化核心服务信息
        
        Args:
            db: 数据库会话
            
        Returns:
            响应结果
        """
        try:
            initialized_count = 0
            
            for service_name, config in ServiceMonitorService.CORE_SERVICES.items():
                # 检查服务是否已存在
                existing_service = ServiceMonitorDao.get_service_info_by_name(db, service_name)
                
                if not existing_service:
                    # 创建新的服务信息
                    service_info = ServiceInfoModel(
                        service_name=service_name,
                        service_type=config['service_type'],
                        display_name=config['display_name'],
                        description=config['description'],
                        port=config.get('port'),
                        health_check_url=config.get('health_check_url'),
                        is_critical=config['is_critical'],
                        config_params={
                            'health_check_type': config.get('health_check_type', 'process')
                        }
                    )
                    
                    ServiceMonitorDao.create_service_info(db, service_info)
                    initialized_count += 1
                    logger.info(f"初始化核心服务: {service_name}")
            
            return ResponseUtil.success(
                data={'initialized_count': initialized_count},
                message=f"成功初始化 {initialized_count} 个核心服务"
            )
            
        except Exception as e:
            logger.error(f"初始化核心服务失败: {str(e)}")
            return ResponseUtil.error(message=f"初始化核心服务失败: {str(e)}")

    @staticmethod
    def check_service_health(service_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查单个服务的健康状态
        
        Args:
            service_name: 服务名称
            config: 服务配置
            
        Returns:
            健康检查结果
        """
        result = {
            'service_name': service_name,
            'status': 'unknown',
            'health_status': 'unknown',
            'response_time_ms': None,
            'error_message': None,
            'check_details': {}
        }
        
        try:
            start_time = time.time()
            
            # 根据检查类型执行不同的健康检查
            check_type = config.get('health_check_type', 'process')
            
            if check_type == 'port':
                # 端口连通性检查
                result.update(ServiceMonitorService._check_port_health(
                    config.get('host', '127.0.0.1'),
                    config.get('port')
                ))
            elif check_type == 'http':
                # HTTP健康检查
                result.update(ServiceMonitorService._check_http_health(
                    config.get('health_check_url')
                ))
            elif check_type == 'process':
                # 进程检查
                result.update(ServiceMonitorService._check_process_health(service_name))
            
            # 计算响应时间
            end_time = time.time()
            result['response_time_ms'] = round((end_time - start_time) * 1000, 2)
            
        except Exception as e:
            result['status'] = 'error'
            result['health_status'] = 'unhealthy'
            result['error_message'] = str(e)
            logger.error(f"服务 {service_name} 健康检查失败: {str(e)}")
        
        return result

    @staticmethod
    def _check_port_health(host: str, port: int) -> Dict[str, Any]:
        """检查端口连通性"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                return {
                    'status': 'running',
                    'health_status': 'healthy',
                    'check_details': {'port_accessible': True}
                }
            else:
                return {
                    'status': 'stopped',
                    'health_status': 'unhealthy',
                    'error_message': f'端口 {port} 不可访问',
                    'check_details': {'port_accessible': False}
                }
        except Exception as e:
            return {
                'status': 'error',
                'health_status': 'unhealthy',
                'error_message': str(e),
                'check_details': {'port_accessible': False}
            }

    @staticmethod
    def _check_http_health(url: str) -> Dict[str, Any]:
        """检查HTTP服务健康状态"""
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                return {
                    'status': 'running',
                    'health_status': 'healthy',
                    'check_details': {
                        'status_code': response.status_code,
                        'response_size': len(response.content)
                    }
                }
            else:
                return {
                    'status': 'running',
                    'health_status': 'unhealthy',
                    'error_message': f'HTTP状态码: {response.status_code}',
                    'check_details': {'status_code': response.status_code}
                }
        except requests.exceptions.ConnectionError:
            return {
                'status': 'stopped',
                'health_status': 'unhealthy',
                'error_message': '连接被拒绝',
                'check_details': {'connection_error': True}
            }
        except requests.exceptions.Timeout:
            return {
                'status': 'running',
                'health_status': 'unhealthy',
                'error_message': '请求超时',
                'check_details': {'timeout': True}
            }
        except Exception as e:
            return {
                'status': 'error',
                'health_status': 'unhealthy',
                'error_message': str(e),
                'check_details': {'exception': str(e)}
            }

    @staticmethod
    def _check_process_health(service_name: str) -> Dict[str, Any]:
        """检查进程健康状态"""
        try:
            # 使用pgrep查找进程
            result = subprocess.run(['pgrep', '-f', service_name], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                return {
                    'status': 'running',
                    'health_status': 'healthy',
                    'check_details': {
                        'process_count': len(pids),
                        'pids': pids
                    }
                }
            else:
                return {
                    'status': 'stopped',
                    'health_status': 'unhealthy',
                    'error_message': '进程未运行',
                    'check_details': {'process_count': 0}
                }
        except Exception as e:
            return {
                'status': 'error',
                'health_status': 'unhealthy',
                'error_message': str(e),
                'check_details': {'exception': str(e)}
            }

    @staticmethod
    def get_service_performance_data(service_name: str) -> Dict[str, Any]:
        """
        获取服务性能数据
        
        Args:
            service_name: 服务名称
            
        Returns:
            性能数据
        """
        try:
            performance_data = {
                'service_name': service_name,
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'memory_used_mb': 0.0,
                'disk_usage': 0.0,
                'network_connections': 0,
                'uptime_seconds': 0
            }
            
            # 获取系统整体性能数据
            performance_data['cpu_usage'] = psutil.cpu_percent(interval=1)
            
            memory = psutil.virtual_memory()
            performance_data['memory_usage'] = memory.percent
            performance_data['memory_used_mb'] = memory.used / 1024 / 1024
            
            disk = psutil.disk_usage('/')
            performance_data['disk_usage'] = disk.percent
            
            # 获取网络连接数
            connections = psutil.net_connections()
            performance_data['network_connections'] = len(connections)
            
            # 获取系统启动时间
            boot_time = psutil.boot_time()
            performance_data['uptime_seconds'] = int(time.time() - boot_time)
            
            return performance_data
            
        except Exception as e:
            logger.error(f"获取服务 {service_name} 性能数据失败: {str(e)}")
            return {
                'service_name': service_name,
                'error': str(e)
            }

    @staticmethod
    def perform_health_check_all_services(db: Session) -> ResponseUtil:
        """
        对所有核心服务执行健康检查
        
        Args:
            db: 数据库会话
            
        Returns:
            健康检查结果
        """
        try:
            results = []
            
            for service_name, config in ServiceMonitorService.CORE_SERVICES.items():
                # 执行健康检查
                health_result = ServiceMonitorService.check_service_health(service_name, config)
                
                # 获取性能数据
                performance_data = ServiceMonitorService.get_service_performance_data(service_name)
                
                # 获取服务信息
                service_info = ServiceMonitorDao.get_service_info_by_name(db, service_name)
                service_id = service_info.service_id if service_info else 0
                
                # 创建服务状态记录
                status_model = ServiceStatusModel(
                    service_id=service_id,
                    service_name=service_name,
                    status=health_result['status'],
                    health_status=health_result['health_status'],
                    cpu_usage=performance_data.get('cpu_usage'),
                    memory_usage=performance_data.get('memory_usage'),
                    memory_used_mb=performance_data.get('memory_used_mb'),
                    disk_usage=performance_data.get('disk_usage'),
                    network_connections=performance_data.get('network_connections'),
                    uptime_seconds=performance_data.get('uptime_seconds'),
                    response_time_ms=health_result.get('response_time_ms'),
                    error_message=health_result.get('error_message')
                )
                
                # 保存状态记录
                ServiceMonitorDao.create_service_status(db, status_model)
                
                # 创建健康检查记录
                check_model = ServiceHealthCheckModel(
                    service_id=service_id,
                    service_name=service_name,
                    check_type=config.get('health_check_type', 'process'),
                    check_result='success' if health_result['health_status'] == 'healthy' else 'failed',
                    response_time_ms=health_result.get('response_time_ms'),
                    error_message=health_result.get('error_message'),
                    check_details=health_result.get('check_details', {})
                )
                
                ServiceMonitorDao.create_health_check_record(db, check_model)
                
                # 检查是否需要生成告警
                if health_result['health_status'] == 'unhealthy' and config.get('is_critical', False):
                    alert_model = ServiceAlertModel(
                        service_id=service_id,
                        service_name=service_name,
                        alert_type='health_check_failed',
                        alert_level='error' if config.get('is_critical') else 'warning',
                        alert_title=f'服务 {service_name} 健康检查失败',
                        alert_message=health_result.get('error_message', '服务不健康'),
                        alert_details=health_result.get('check_details', {})
                    )
                    
                    ServiceMonitorDao.create_service_alert(db, alert_model)
                
                results.append({
                    'service_name': service_name,
                    'display_name': config['display_name'],
                    'status': health_result['status'],
                    'health_status': health_result['health_status'],
                    'response_time_ms': health_result.get('response_time_ms'),
                    'error_message': health_result.get('error_message'),
                    'performance': performance_data
                })
            
            return ResponseUtil.success(
                data={'services': results},
                message="健康检查完成"
            )
            
        except Exception as e:
            logger.error(f"执行健康检查失败: {str(e)}")
            return ResponseUtil.error(message=f"执行健康检查失败: {str(e)}")

    @staticmethod
    def get_dashboard_data(db: Session) -> ResponseUtil:
        """
        获取监控仪表板数据
        
        Args:
            db: 数据库会话
            
        Returns:
            仪表板数据
        """
        try:
            # 获取统计数据
            stats = ServiceMonitorDao.get_dashboard_statistics(db)
            
            # 获取最近的操作记录
            recent_operations_query = ServiceOperationQueryModel(page_size=5)
            recent_operations, _ = ServiceMonitorDao.get_service_operations(db, recent_operations_query)
            
            dashboard_data = ServiceDashboardModel(
                total_services=stats['total_services'],
                running_services=stats['running_services'],
                stopped_services=stats['stopped_services'],
                error_services=stats['error_services'],
                critical_services=stats['critical_services'],
                healthy_services=stats['healthy_services'],
                unhealthy_services=stats['unhealthy_services'],
                avg_cpu_usage=stats['avg_cpu_usage'],
                avg_memory_usage=stats['avg_memory_usage'],
                total_alerts=stats['total_alerts'],
                unresolved_alerts=stats['unresolved_alerts'],
                recent_operations=[
                    ServiceOperationModel.from_orm(op) for op in recent_operations
                ]
            )
            
            return ResponseUtil.success(
                data=dashboard_data.dict(),
                message="获取仪表板数据成功"
            )
            
        except Exception as e:
            logger.error(f"获取仪表板数据失败: {str(e)}")
            return ResponseUtil.error(message=f"获取仪表板数据失败: {str(e)}")

    @staticmethod
    def get_service_performance_chart_data(db: Session, service_name: str, time_range: str = '24h') -> ResponseUtil:
        """
        获取服务性能图表数据

        Args:
            db: 数据库会话
            service_name: 服务名称
            time_range: 时间范围 (1h, 6h, 24h, 7d)

        Returns:
            性能图表数据
        """
        try:
            # 解析时间范围
            hours_map = {'1h': 1, '6h': 6, '24h': 24, '7d': 168}
            hours = hours_map.get(time_range, 24)

            # 获取服务状态历史数据
            status_history = ServiceMonitorDao.get_service_status_history(db, service_name, hours)

            # 构建图表数据
            cpu_data = []
            memory_data = []
            response_time_data = []
            network_data = []

            for status in reversed(status_history):  # 按时间正序排列
                timestamp = status.created_time.strftime('%Y-%m-%d %H:%M:%S')

                if status.cpu_usage is not None:
                    cpu_data.append({
                        'time': timestamp,
                        'value': status.cpu_usage
                    })

                if status.memory_usage is not None:
                    memory_data.append({
                        'time': timestamp,
                        'value': status.memory_usage
                    })

                if status.response_time_ms is not None:
                    response_time_data.append({
                        'time': timestamp,
                        'value': status.response_time_ms
                    })

                if status.network_connections is not None:
                    network_data.append({
                        'time': timestamp,
                        'value': status.network_connections
                    })

            chart_data = ServicePerformanceChartModel(
                service_name=service_name,
                time_range=time_range,
                cpu_data=cpu_data,
                memory_data=memory_data,
                response_time_data=response_time_data,
                network_data=network_data
            )

            return ResponseUtil.success(
                data=chart_data.dict(),
                message="获取性能图表数据成功"
            )

        except Exception as e:
            logger.error(f"获取服务 {service_name} 性能图表数据失败: {str(e)}")
            return ResponseUtil.error(message=f"获取性能图表数据失败: {str(e)}")

    @staticmethod
    def restart_service(db: Session, service_name: str, operator: str, operator_ip: str = None) -> ResponseUtil:
        """
        重启服务

        Args:
            db: 数据库会话
            service_name: 服务名称
            operator: 操作人
            operator_ip: 操作人IP

        Returns:
            操作结果
        """
        try:
            # 获取服务信息
            service_info = ServiceMonitorDao.get_service_info_by_name(db, service_name)
            if not service_info:
                return ResponseUtil.error(message=f"服务 {service_name} 不存在")

            # 创建操作记录
            operation_model = ServiceOperationModel(
                service_id=service_info.service_id,
                service_name=service_name,
                operation_type='restart',
                operation_status='running',
                operator=operator,
                operator_ip=operator_ip
            )

            operation_record = ServiceMonitorDao.create_service_operation(db, operation_model)

            start_time = time.time()
            success = False
            error_message = None

            try:
                # 执行重启操作
                if service_name in ['mysql', 'redis']:
                    # 系统服务重启
                    result = subprocess.run(['sudo', 'systemctl', 'restart', service_name],
                                          capture_output=True, text=True, timeout=30)
                    success = result.returncode == 0
                    if not success:
                        error_message = result.stderr
                else:
                    # 应用服务重启 - 这里需要根据实际情况调整
                    # 可以调用运维脚本或者发送重启信号
                    restart_script = f"/path/to/restart_{service_name}.sh"
                    result = subprocess.run([restart_script],
                                          capture_output=True, text=True, timeout=60)
                    success = result.returncode == 0
                    if not success:
                        error_message = result.stderr

            except subprocess.TimeoutExpired:
                error_message = "重启操作超时"
            except Exception as e:
                error_message = str(e)

            # 计算执行时间
            execution_time_ms = int((time.time() - start_time) * 1000)

            # 更新操作记录
            ServiceMonitorDao.update_service_operation(
                db, operation_record.operation_id,
                operation_status='success' if success else 'failed',
                operation_result='重启成功' if success else '重启失败',
                error_message=error_message,
                execution_time_ms=execution_time_ms,
                completed_time=datetime.now()
            )

            # 记录操作日志
            log_model = ServiceLogModel(
                service_id=service_info.service_id,
                service_name=service_name,
                log_level='INFO' if success else 'ERROR',
                log_source='service_monitor',
                log_message=f"服务重启{'成功' if success else '失败'}",
                log_details={
                    'operator': operator,
                    'execution_time_ms': execution_time_ms,
                    'error_message': error_message
                },
                user_id=operator,
                ip_address=operator_ip
            )

            ServiceMonitorDao.create_service_log(db, log_model)

            if success:
                return ResponseUtil.success(
                    data={'execution_time_ms': execution_time_ms},
                    message=f"服务 {service_name} 重启成功"
                )
            else:
                return ResponseUtil.error(
                    message=f"服务 {service_name} 重启失败: {error_message}"
                )

        except Exception as e:
            logger.error(f"重启服务 {service_name} 失败: {str(e)}")
            return ResponseUtil.error(message=f"重启服务失败: {str(e)}")

    @staticmethod
    def stop_service(db: Session, service_name: str, operator: str, operator_ip: str = None) -> ResponseUtil:
        """
        停止服务

        Args:
            db: 数据库会话
            service_name: 服务名称
            operator: 操作人
            operator_ip: 操作人IP

        Returns:
            操作结果
        """
        try:
            # 获取服务信息
            service_info = ServiceMonitorDao.get_service_info_by_name(db, service_name)
            if not service_info:
                return ResponseUtil.error(message=f"服务 {service_name} 不存在")

            # 检查是否为关键服务
            if service_info.is_critical:
                return ResponseUtil.error(message=f"服务 {service_name} 是关键服务，不允许停止")

            # 创建操作记录
            operation_model = ServiceOperationModel(
                service_id=service_info.service_id,
                service_name=service_name,
                operation_type='stop',
                operation_status='running',
                operator=operator,
                operator_ip=operator_ip
            )

            operation_record = ServiceMonitorDao.create_service_operation(db, operation_model)

            start_time = time.time()
            success = False
            error_message = None

            try:
                # 执行停止操作
                if service_name in ['mysql', 'redis']:
                    # 系统服务停止
                    result = subprocess.run(['sudo', 'systemctl', 'stop', service_name],
                                          capture_output=True, text=True, timeout=30)
                    success = result.returncode == 0
                    if not success:
                        error_message = result.stderr
                else:
                    # 应用服务停止
                    stop_script = f"/path/to/stop_{service_name}.sh"
                    result = subprocess.run([stop_script],
                                          capture_output=True, text=True, timeout=30)
                    success = result.returncode == 0
                    if not success:
                        error_message = result.stderr

            except subprocess.TimeoutExpired:
                error_message = "停止操作超时"
            except Exception as e:
                error_message = str(e)

            # 计算执行时间
            execution_time_ms = int((time.time() - start_time) * 1000)

            # 更新操作记录
            ServiceMonitorDao.update_service_operation(
                db, operation_record.operation_id,
                operation_status='success' if success else 'failed',
                operation_result='停止成功' if success else '停止失败',
                error_message=error_message,
                execution_time_ms=execution_time_ms,
                completed_time=datetime.now()
            )

            # 记录操作日志
            log_model = ServiceLogModel(
                service_id=service_info.service_id,
                service_name=service_name,
                log_level='INFO' if success else 'ERROR',
                log_source='service_monitor',
                log_message=f"服务停止{'成功' if success else '失败'}",
                log_details={
                    'operator': operator,
                    'execution_time_ms': execution_time_ms,
                    'error_message': error_message
                },
                user_id=operator,
                ip_address=operator_ip
            )

            ServiceMonitorDao.create_service_log(db, log_model)

            if success:
                return ResponseUtil.success(
                    data={'execution_time_ms': execution_time_ms},
                    message=f"服务 {service_name} 停止成功"
                )
            else:
                return ResponseUtil.error(
                    message=f"服务 {service_name} 停止失败: {error_message}"
                )

        except Exception as e:
            logger.error(f"停止服务 {service_name} 失败: {str(e)}")
            return ResponseUtil.error(message=f"停止服务失败: {str(e)}")

    @staticmethod
    def start_service(db: Session, service_name: str, operator: str, operator_ip: str = None) -> ResponseUtil:
        """
        启动服务

        Args:
            db: 数据库会话
            service_name: 服务名称
            operator: 操作人
            operator_ip: 操作人IP

        Returns:
            操作结果
        """
        try:
            # 获取服务信息
            service_info = ServiceMonitorDao.get_service_info_by_name(db, service_name)
            if not service_info:
                return ResponseUtil.error(message=f"服务 {service_name} 不存在")

            # 创建操作记录
            operation_model = ServiceOperationModel(
                service_id=service_info.service_id,
                service_name=service_name,
                operation_type='start',
                operation_status='running',
                operator=operator,
                operator_ip=operator_ip
            )

            operation_record = ServiceMonitorDao.create_service_operation(db, operation_model)

            start_time = time.time()
            success = False
            error_message = None

            try:
                # 执行启动操作
                if service_name in ['mysql', 'redis']:
                    # 系统服务启动
                    result = subprocess.run(['sudo', 'systemctl', 'start', service_name],
                                          capture_output=True, text=True, timeout=30)
                    success = result.returncode == 0
                    if not success:
                        error_message = result.stderr
                else:
                    # 应用服务启动
                    start_script = f"/path/to/start_{service_name}.sh"
                    result = subprocess.run([start_script],
                                          capture_output=True, text=True, timeout=60)
                    success = result.returncode == 0
                    if not success:
                        error_message = result.stderr

            except subprocess.TimeoutExpired:
                error_message = "启动操作超时"
            except Exception as e:
                error_message = str(e)

            # 计算执行时间
            execution_time_ms = int((time.time() - start_time) * 1000)

            # 更新操作记录
            ServiceMonitorDao.update_service_operation(
                db, operation_record.operation_id,
                operation_status='success' if success else 'failed',
                operation_result='启动成功' if success else '启动失败',
                error_message=error_message,
                execution_time_ms=execution_time_ms,
                completed_time=datetime.now()
            )

            # 记录操作日志
            log_model = ServiceLogModel(
                service_id=service_info.service_id,
                service_name=service_name,
                log_level='INFO' if success else 'ERROR',
                log_source='service_monitor',
                log_message=f"服务启动{'成功' if success else '失败'}",
                log_details={
                    'operator': operator,
                    'execution_time_ms': execution_time_ms,
                    'error_message': error_message
                },
                user_id=operator,
                ip_address=operator_ip
            )

            ServiceMonitorDao.create_service_log(db, log_model)

            if success:
                return ResponseUtil.success(
                    data={'execution_time_ms': execution_time_ms},
                    message=f"服务 {service_name} 启动成功"
                )
            else:
                return ResponseUtil.error(
                    message=f"服务 {service_name} 启动失败: {error_message}"
                )

        except Exception as e:
            logger.error(f"启动服务 {service_name} 失败: {str(e)}")
            return ResponseUtil.error(message=f"启动服务失败: {str(e)}")
