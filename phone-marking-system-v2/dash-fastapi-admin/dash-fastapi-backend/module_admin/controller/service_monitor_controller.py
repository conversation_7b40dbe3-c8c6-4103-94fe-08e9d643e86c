"""
服务监控控制器
提供服务监控相关的API接口
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session
from typing import List, Optional

from config.get_db import get_db
from module_admin.service.service_monitor_service import ServiceMonitorService
from module_admin.dao.service_monitor_dao import ServiceMonitorDao
from module_admin.entity.vo.service_monitor_vo import (
    ServiceInfoModel, ServiceStatusModel, ServiceHealthCheckModel,
    ServicePerformanceMetricsModel, ServiceAlertModel, ServiceLogModel,
    ServiceOperationModel, ServiceQueryModel, ServiceLogQueryModel,
    ServiceAlertQueryModel, ServiceOperationQueryModel
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.common_util import CamelCaseUtil
from module_admin.annotation.log_annotation import log_decorator
from module_admin.service.login_service import get_current_user
from module_admin.entity.vo.login_vo import CurrentUserInfoServiceResponse
from module_admin.annotation.auth_annotation import CheckUserInterfaceAuth


# 创建路由器
serviceMonitorController = APIRouter(prefix="/monitor", tags=["服务监控管理"])


@serviceMonitorController.post("/init", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:init'))])
@log_decorator(title='服务监控', business_type=1)
async def init_core_services(
    request: Request,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    初始化核心服务
    """
    try:
        result = ServiceMonitorService.initialize_core_services(query_db)
        logger.info(f"用户 {current_user.user.user_name} 初始化核心服务")
        return result.to_dict()
    except Exception as e:
        logger.error(f"初始化核心服务失败: {str(e)}")
        return ResponseUtil.error(message=f"初始化核心服务失败: {str(e)}").to_dict()


@serviceMonitorController.get("/dashboard", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:query'))])
async def get_dashboard_data(
    request: Request,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    获取监控仪表板数据
    """
    try:
        result = ServiceMonitorService.get_dashboard_data(query_db)
        return result.to_dict()
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {str(e)}")
        return ResponseUtil.error(message=f"获取仪表板数据失败: {str(e)}").to_dict()


@serviceMonitorController.post("/health-check", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:check'))])
@log_decorator(title='服务监控', business_type=4)
async def perform_health_check(
    request: Request,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    执行所有服务健康检查
    """
    try:
        result = ServiceMonitorService.perform_health_check_all_services(query_db)
        logger.info(f"用户 {current_user.user.user_name} 执行服务健康检查")
        return result.to_dict()
    except Exception as e:
        logger.error(f"执行健康检查失败: {str(e)}")
        return ResponseUtil.error(message=f"执行健康检查失败: {str(e)}").to_dict()


@serviceMonitorController.get("/services", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:query'))])
async def get_service_list(
    request: Request,
    service_name: Optional[str] = None,
    service_type: Optional[str] = None,
    status: Optional[str] = None,
    is_critical: Optional[bool] = None,
    page_num: int = 1,
    page_size: int = 10,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    获取服务列表
    """
    try:
        query_model = ServiceQueryModel(
            service_name=service_name,
            service_type=service_type,
            status=status,
            is_critical=is_critical,
            page_num=page_num,
            page_size=page_size
        )
        
        services, total = ServiceMonitorDao.get_service_info_list(query_db, query_model)
        
        # 获取每个服务的最新状态
        service_list = []
        for service in services:
            latest_status = ServiceMonitorDao.get_latest_service_status(query_db, service.service_name)
            
            service_data = CamelCaseUtil.transform_dict_key_to_camel_case(service.__dict__)
            if latest_status:
                service_data['latest_status'] = CamelCaseUtil.transform_dict_key_to_camel_case(latest_status.__dict__)
            
            service_list.append(service_data)
        
        return ResponseUtil.success(
            data={
                'services': service_list,
                'total': total,
                'page_num': page_num,
                'page_size': page_size
            },
            message="获取服务列表成功"
        ).to_dict()
        
    except Exception as e:
        logger.error(f"获取服务列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取服务列表失败: {str(e)}").to_dict()


@serviceMonitorController.get("/services/{service_name}/performance", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:query'))])
async def get_service_performance_chart(
    service_name: str,
    time_range: str = '24h',
    request: Request = None,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    获取服务性能图表数据
    """
    try:
        result = ServiceMonitorService.get_service_performance_chart_data(query_db, service_name, time_range)
        return result.to_dict()
    except Exception as e:
        logger.error(f"获取服务性能图表数据失败: {str(e)}")
        return ResponseUtil.error(message=f"获取性能图表数据失败: {str(e)}").to_dict()


@serviceMonitorController.post("/services/{service_name}/restart", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:restart'))])
@log_decorator(title='服务监控', business_type=2)
async def restart_service(
    service_name: str,
    request: Request,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    重启服务
    """
    try:
        client_ip = request.client.host if request.client else None
        result = ServiceMonitorService.restart_service(
            query_db, service_name, current_user.user.user_name, client_ip
        )
        logger.info(f"用户 {current_user.user.user_name} 重启服务 {service_name}")
        return result.to_dict()
    except Exception as e:
        logger.error(f"重启服务失败: {str(e)}")
        return ResponseUtil.error(message=f"重启服务失败: {str(e)}").to_dict()


@serviceMonitorController.post("/services/{service_name}/start", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:start'))])
@log_decorator(title='服务监控', business_type=2)
async def start_service(
    service_name: str,
    request: Request,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    启动服务
    """
    try:
        client_ip = request.client.host if request.client else None
        result = ServiceMonitorService.start_service(
            query_db, service_name, current_user.user.user_name, client_ip
        )
        logger.info(f"用户 {current_user.user.user_name} 启动服务 {service_name}")
        return result.to_dict()
    except Exception as e:
        logger.error(f"启动服务失败: {str(e)}")
        return ResponseUtil.error(message=f"启动服务失败: {str(e)}").to_dict()


@serviceMonitorController.post("/services/{service_name}/stop", dependencies=[Depends(CheckUserInterfaceAuth('monitor:service:stop'))])
@log_decorator(title='服务监控', business_type=2)
async def stop_service(
    service_name: str,
    request: Request,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    停止服务
    """
    try:
        client_ip = request.client.host if request.client else None
        result = ServiceMonitorService.stop_service(
            query_db, service_name, current_user.user.user_name, client_ip
        )
        logger.info(f"用户 {current_user.user.user_name} 停止服务 {service_name}")
        return result.to_dict()
    except Exception as e:
        logger.error(f"停止服务失败: {str(e)}")
        return ResponseUtil.error(message=f"停止服务失败: {str(e)}").to_dict()


@serviceMonitorController.get("/alerts", dependencies=[Depends(CheckUserInterfaceAuth('monitor:alert:query'))])
async def get_service_alerts(
    request: Request,
    service_name: Optional[str] = None,
    alert_level: Optional[str] = None,
    alert_type: Optional[str] = None,
    is_resolved: Optional[bool] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    page_num: int = 1,
    page_size: int = 20,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    获取服务告警列表
    """
    try:
        from datetime import datetime
        
        query_model = ServiceAlertQueryModel(
            service_name=service_name,
            alert_level=alert_level,
            alert_type=alert_type,
            is_resolved=is_resolved,
            start_time=datetime.fromisoformat(start_time) if start_time else None,
            end_time=datetime.fromisoformat(end_time) if end_time else None,
            page_num=page_num,
            page_size=page_size
        )
        
        alerts, total = ServiceMonitorDao.get_service_alerts(query_db, query_model)
        
        alert_list = [CamelCaseUtil.transform_dict_key_to_camel_case(alert.__dict__) for alert in alerts]
        
        return ResponseUtil.success(
            data={
                'alerts': alert_list,
                'total': total,
                'page_num': page_num,
                'page_size': page_size
            },
            message="获取告警列表成功"
        ).to_dict()
        
    except Exception as e:
        logger.error(f"获取告警列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取告警列表失败: {str(e)}").to_dict()


@serviceMonitorController.post("/alerts/{alert_id}/resolve", dependencies=[Depends(CheckUserInterfaceAuth('monitor:alert:resolve'))])
@log_decorator(title='服务监控', business_type=2)
async def resolve_alert(
    alert_id: int,
    resolution_notes: Optional[str] = None,
    request: Request = None,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    解决告警
    """
    try:
        result = ServiceMonitorDao.resolve_service_alert(
            query_db, alert_id, current_user.user.user_name, resolution_notes
        )
        
        if result:
            logger.info(f"用户 {current_user.user.user_name} 解决告警 {alert_id}")
            return ResponseUtil.success(message="告警已解决").to_dict()
        else:
            return ResponseUtil.error(message="告警不存在").to_dict()
            
    except Exception as e:
        logger.error(f"解决告警失败: {str(e)}")
        return ResponseUtil.error(message=f"解决告警失败: {str(e)}").to_dict()


@serviceMonitorController.get("/logs", dependencies=[Depends(CheckUserInterfaceAuth('monitor:log:query'))])
async def get_service_logs(
    request: Request,
    service_name: Optional[str] = None,
    log_level: Optional[str] = None,
    keyword: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    page_num: int = 1,
    page_size: int = 50,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    获取服务日志列表
    """
    try:
        from datetime import datetime
        
        query_model = ServiceLogQueryModel(
            service_name=service_name,
            log_level=log_level,
            keyword=keyword,
            start_time=datetime.fromisoformat(start_time) if start_time else None,
            end_time=datetime.fromisoformat(end_time) if end_time else None,
            page_num=page_num,
            page_size=page_size
        )
        
        logs, total = ServiceMonitorDao.get_service_logs(query_db, query_model)
        
        log_list = [CamelCaseUtil.transform_dict_key_to_camel_case(log.__dict__) for log in logs]
        
        return ResponseUtil.success(
            data={
                'logs': log_list,
                'total': total,
                'page_num': page_num,
                'page_size': page_size
            },
            message="获取日志列表成功"
        ).to_dict()
        
    except Exception as e:
        logger.error(f"获取日志列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取日志列表失败: {str(e)}").to_dict()


@serviceMonitorController.get("/operations", dependencies=[Depends(CheckUserInterfaceAuth('monitor:operation:query'))])
async def get_service_operations(
    request: Request,
    service_name: Optional[str] = None,
    operation_type: Optional[str] = None,
    operation_status: Optional[str] = None,
    operator: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    page_num: int = 1,
    page_size: int = 20,
    query_db: Session = Depends(get_db),
    current_user: CurrentUserInfoServiceResponse = Depends(get_current_user)
):
    """
    获取服务操作记录列表
    """
    try:
        from datetime import datetime
        
        query_model = ServiceOperationQueryModel(
            service_name=service_name,
            operation_type=operation_type,
            operation_status=operation_status,
            operator=operator,
            start_time=datetime.fromisoformat(start_time) if start_time else None,
            end_time=datetime.fromisoformat(end_time) if end_time else None,
            page_num=page_num,
            page_size=page_size
        )
        
        operations, total = ServiceMonitorDao.get_service_operations(query_db, query_model)
        
        operation_list = [CamelCaseUtil.transform_dict_key_to_camel_case(op.__dict__) for op in operations]
        
        return ResponseUtil.success(
            data={
                'operations': operation_list,
                'total': total,
                'page_num': page_num,
                'page_size': page_size
            },
            message="获取操作记录成功"
        ).to_dict()
        
    except Exception as e:
        logger.error(f"获取操作记录失败: {str(e)}")
        return ResponseUtil.error(message=f"获取操作记录失败: {str(e)}").to_dict()
