"""
服务监控数据访问层
提供服务监控相关的数据库操作方法
"""
from sqlalchemy import and_, or_, desc, func, text
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta

from module_admin.entity.do.service_monitor_do import (
    ServiceInfo, ServiceStatus, ServiceHealthCheck, ServicePerformanceMetrics,
    ServiceAlert, ServiceLog, ServiceOperation
)
from module_admin.entity.vo.service_monitor_vo import (
    ServiceInfoModel, ServiceStatusModel, ServiceHealthCheckModel,
    ServicePerformanceMetricsModel, ServiceAlertModel, ServiceLogModel,
    ServiceOperationModel, ServiceQueryModel, ServiceLogQueryModel,
    ServiceAlertQueryModel, ServiceOperationQueryModel
)


class ServiceMonitorDao:
    """服务监控数据访问对象"""

    @staticmethod
    def get_service_info_list(db: Session, query_model: ServiceQueryModel) -> <PERSON><PERSON>[List[ServiceInfo], int]:
        """
        获取服务信息列表
        
        Args:
            db: 数据库会话
            query_model: 查询模型
            
        Returns:
            服务信息列表和总数
        """
        query = db.query(ServiceInfo)
        
        # 构建查询条件
        if query_model.service_name:
            query = query.filter(ServiceInfo.service_name.like(f'%{query_model.service_name}%'))
        if query_model.service_type:
            query = query.filter(ServiceInfo.service_type == query_model.service_type)
        if query_model.status:
            query = query.filter(ServiceInfo.status == query_model.status)
        if query_model.is_critical is not None:
            query = query.filter(ServiceInfo.is_critical == query_model.is_critical)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        services = query.order_by(desc(ServiceInfo.created_time))\
                       .offset((query_model.page_num - 1) * query_model.page_size)\
                       .limit(query_model.page_size)\
                       .all()
        
        return services, total

    @staticmethod
    def get_service_info_by_id(db: Session, service_id: int) -> Optional[ServiceInfo]:
        """根据ID获取服务信息"""
        return db.query(ServiceInfo).filter(ServiceInfo.service_id == service_id).first()

    @staticmethod
    def get_service_info_by_name(db: Session, service_name: str) -> Optional[ServiceInfo]:
        """根据名称获取服务信息"""
        return db.query(ServiceInfo).filter(ServiceInfo.service_name == service_name).first()

    @staticmethod
    def create_service_info(db: Session, service_info: ServiceInfoModel) -> ServiceInfo:
        """创建服务信息"""
        db_service = ServiceInfo(**service_info.dict(exclude={'service_id'}))
        db.add(db_service)
        db.commit()
        db.refresh(db_service)
        return db_service

    @staticmethod
    def update_service_info(db: Session, service_id: int, service_info: ServiceInfoModel) -> Optional[ServiceInfo]:
        """更新服务信息"""
        db_service = db.query(ServiceInfo).filter(ServiceInfo.service_id == service_id).first()
        if db_service:
            update_data = service_info.dict(exclude_unset=True, exclude={'service_id'})
            for field, value in update_data.items():
                setattr(db_service, field, value)
            db_service.updated_time = datetime.now()
            db.commit()
            db.refresh(db_service)
        return db_service

    @staticmethod
    def delete_service_info(db: Session, service_id: int) -> bool:
        """删除服务信息"""
        db_service = db.query(ServiceInfo).filter(ServiceInfo.service_id == service_id).first()
        if db_service:
            db.delete(db_service)
            db.commit()
            return True
        return False

    @staticmethod
    def get_latest_service_status(db: Session, service_name: str) -> Optional[ServiceStatus]:
        """获取服务最新状态"""
        return db.query(ServiceStatus)\
                 .filter(ServiceStatus.service_name == service_name)\
                 .order_by(desc(ServiceStatus.created_time))\
                 .first()

    @staticmethod
    def create_service_status(db: Session, status_model: ServiceStatusModel) -> ServiceStatus:
        """创建服务状态记录"""
        db_status = ServiceStatus(**status_model.dict(exclude={'status_id'}))
        db.add(db_status)
        db.commit()
        db.refresh(db_status)
        return db_status

    @staticmethod
    def get_service_status_history(db: Session, service_name: str, hours: int = 24) -> List[ServiceStatus]:
        """获取服务状态历史记录"""
        start_time = datetime.now() - timedelta(hours=hours)
        return db.query(ServiceStatus)\
                 .filter(and_(
                     ServiceStatus.service_name == service_name,
                     ServiceStatus.created_time >= start_time
                 ))\
                 .order_by(desc(ServiceStatus.created_time))\
                 .all()

    @staticmethod
    def get_all_services_latest_status(db: Session) -> List[ServiceStatus]:
        """获取所有服务的最新状态"""
        # 使用子查询获取每个服务的最新状态记录
        subquery = db.query(
            ServiceStatus.service_name,
            func.max(ServiceStatus.created_time).label('max_time')
        ).group_by(ServiceStatus.service_name).subquery()
        
        return db.query(ServiceStatus)\
                 .join(subquery, and_(
                     ServiceStatus.service_name == subquery.c.service_name,
                     ServiceStatus.created_time == subquery.c.max_time
                 ))\
                 .all()

    @staticmethod
    def create_health_check_record(db: Session, check_model: ServiceHealthCheckModel) -> ServiceHealthCheck:
        """创建健康检查记录"""
        db_check = ServiceHealthCheck(**check_model.dict(exclude={'check_id'}))
        db.add(db_check)
        db.commit()
        db.refresh(db_check)
        return db_check

    @staticmethod
    def get_health_check_history(db: Session, service_name: str, hours: int = 24) -> List[ServiceHealthCheck]:
        """获取健康检查历史记录"""
        start_time = datetime.now() - timedelta(hours=hours)
        return db.query(ServiceHealthCheck)\
                 .filter(and_(
                     ServiceHealthCheck.service_name == service_name,
                     ServiceHealthCheck.check_time >= start_time
                 ))\
                 .order_by(desc(ServiceHealthCheck.check_time))\
                 .all()

    @staticmethod
    def create_performance_metric(db: Session, metric_model: ServicePerformanceMetricsModel) -> ServicePerformanceMetrics:
        """创建性能指标记录"""
        db_metric = ServicePerformanceMetrics(**metric_model.dict(exclude={'metric_id'}))
        db.add(db_metric)
        db.commit()
        db.refresh(db_metric)
        return db_metric

    @staticmethod
    def get_performance_metrics(db: Session, service_name: str, metric_name: str, hours: int = 24) -> List[ServicePerformanceMetrics]:
        """获取性能指标数据"""
        start_time = datetime.now() - timedelta(hours=hours)
        return db.query(ServicePerformanceMetrics)\
                 .filter(and_(
                     ServicePerformanceMetrics.service_name == service_name,
                     ServicePerformanceMetrics.metric_name == metric_name,
                     ServicePerformanceMetrics.timestamp >= start_time
                 ))\
                 .order_by(ServicePerformanceMetrics.timestamp)\
                 .all()

    @staticmethod
    def create_service_alert(db: Session, alert_model: ServiceAlertModel) -> ServiceAlert:
        """创建服务告警"""
        db_alert = ServiceAlert(**alert_model.dict(exclude={'alert_id'}))
        db.add(db_alert)
        db.commit()
        db.refresh(db_alert)
        return db_alert

    @staticmethod
    def get_service_alerts(db: Session, query_model: ServiceAlertQueryModel) -> Tuple[List[ServiceAlert], int]:
        """获取服务告警列表"""
        query = db.query(ServiceAlert)
        
        # 构建查询条件
        if query_model.service_name:
            query = query.filter(ServiceAlert.service_name.like(f'%{query_model.service_name}%'))
        if query_model.alert_level:
            query = query.filter(ServiceAlert.alert_level == query_model.alert_level)
        if query_model.alert_type:
            query = query.filter(ServiceAlert.alert_type == query_model.alert_type)
        if query_model.is_resolved is not None:
            query = query.filter(ServiceAlert.is_resolved == query_model.is_resolved)
        if query_model.start_time:
            query = query.filter(ServiceAlert.alert_time >= query_model.start_time)
        if query_model.end_time:
            query = query.filter(ServiceAlert.alert_time <= query_model.end_time)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        alerts = query.order_by(desc(ServiceAlert.alert_time))\
                     .offset((query_model.page_num - 1) * query_model.page_size)\
                     .limit(query_model.page_size)\
                     .all()
        
        return alerts, total

    @staticmethod
    def resolve_service_alert(db: Session, alert_id: int, resolved_by: str, resolution_notes: str = None) -> Optional[ServiceAlert]:
        """解决服务告警"""
        db_alert = db.query(ServiceAlert).filter(ServiceAlert.alert_id == alert_id).first()
        if db_alert:
            db_alert.is_resolved = True
            db_alert.resolved_time = datetime.now()
            db_alert.resolved_by = resolved_by
            if resolution_notes:
                db_alert.resolution_notes = resolution_notes
            db.commit()
            db.refresh(db_alert)
        return db_alert

    @staticmethod
    def create_service_log(db: Session, log_model: ServiceLogModel) -> ServiceLog:
        """创建服务日志记录"""
        db_log = ServiceLog(**log_model.dict(exclude={'log_id'}))
        db.add(db_log)
        db.commit()
        db.refresh(db_log)
        return db_log

    @staticmethod
    def get_service_logs(db: Session, query_model: ServiceLogQueryModel) -> Tuple[List[ServiceLog], int]:
        """获取服务日志列表"""
        query = db.query(ServiceLog)
        
        # 构建查询条件
        if query_model.service_name:
            query = query.filter(ServiceLog.service_name.like(f'%{query_model.service_name}%'))
        if query_model.log_level:
            query = query.filter(ServiceLog.log_level == query_model.log_level)
        if query_model.keyword:
            query = query.filter(ServiceLog.log_message.like(f'%{query_model.keyword}%'))
        if query_model.start_time:
            query = query.filter(ServiceLog.log_time >= query_model.start_time)
        if query_model.end_time:
            query = query.filter(ServiceLog.log_time <= query_model.end_time)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        logs = query.order_by(desc(ServiceLog.log_time))\
                   .offset((query_model.page_num - 1) * query_model.page_size)\
                   .limit(query_model.page_size)\
                   .all()
        
        return logs, total

    @staticmethod
    def create_service_operation(db: Session, operation_model: ServiceOperationModel) -> ServiceOperation:
        """创建服务操作记录"""
        db_operation = ServiceOperation(**operation_model.dict(exclude={'operation_id'}))
        db.add(db_operation)
        db.commit()
        db.refresh(db_operation)
        return db_operation

    @staticmethod
    def update_service_operation(db: Session, operation_id: int, **kwargs) -> Optional[ServiceOperation]:
        """更新服务操作记录"""
        db_operation = db.query(ServiceOperation).filter(ServiceOperation.operation_id == operation_id).first()
        if db_operation:
            for field, value in kwargs.items():
                if hasattr(db_operation, field):
                    setattr(db_operation, field, value)
            db.commit()
            db.refresh(db_operation)
        return db_operation

    @staticmethod
    def get_service_operations(db: Session, query_model: ServiceOperationQueryModel) -> Tuple[List[ServiceOperation], int]:
        """获取服务操作记录列表"""
        query = db.query(ServiceOperation)
        
        # 构建查询条件
        if query_model.service_name:
            query = query.filter(ServiceOperation.service_name.like(f'%{query_model.service_name}%'))
        if query_model.operation_type:
            query = query.filter(ServiceOperation.operation_type == query_model.operation_type)
        if query_model.operation_status:
            query = query.filter(ServiceOperation.operation_status == query_model.operation_status)
        if query_model.operator:
            query = query.filter(ServiceOperation.operator.like(f'%{query_model.operator}%'))
        if query_model.start_time:
            query = query.filter(ServiceOperation.operation_time >= query_model.start_time)
        if query_model.end_time:
            query = query.filter(ServiceOperation.operation_time <= query_model.end_time)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        operations = query.order_by(desc(ServiceOperation.operation_time))\
                         .offset((query_model.page_num - 1) * query_model.page_size)\
                         .limit(query_model.page_size)\
                         .all()
        
        return operations, total

    @staticmethod
    def get_dashboard_statistics(db: Session) -> Dict[str, Any]:
        """获取仪表板统计数据"""
        # 获取所有服务的最新状态
        latest_statuses = ServiceMonitorDao.get_all_services_latest_status(db)
        
        # 统计各种状态的服务数量
        total_services = len(latest_statuses)
        running_services = sum(1 for s in latest_statuses if s.status == 'running')
        stopped_services = sum(1 for s in latest_statuses if s.status == 'stopped')
        error_services = sum(1 for s in latest_statuses if s.status == 'error')
        healthy_services = sum(1 for s in latest_statuses if s.health_status == 'healthy')
        unhealthy_services = sum(1 for s in latest_statuses if s.health_status == 'unhealthy')
        
        # 计算平均资源使用率
        cpu_values = [s.cpu_usage for s in latest_statuses if s.cpu_usage is not None]
        memory_values = [s.memory_usage for s in latest_statuses if s.memory_usage is not None]
        avg_cpu_usage = sum(cpu_values) / len(cpu_values) if cpu_values else 0
        avg_memory_usage = sum(memory_values) / len(memory_values) if memory_values else 0
        
        # 获取关键服务数量
        critical_services = db.query(ServiceInfo).filter(ServiceInfo.is_critical == True).count()
        
        # 获取告警统计
        total_alerts = db.query(ServiceAlert).count()
        unresolved_alerts = db.query(ServiceAlert).filter(ServiceAlert.is_resolved == False).count()
        
        return {
            'total_services': total_services,
            'running_services': running_services,
            'stopped_services': stopped_services,
            'error_services': error_services,
            'critical_services': critical_services,
            'healthy_services': healthy_services,
            'unhealthy_services': unhealthy_services,
            'avg_cpu_usage': round(avg_cpu_usage, 2),
            'avg_memory_usage': round(avg_memory_usage, 2),
            'total_alerts': total_alerts,
            'unresolved_alerts': unresolved_alerts
        }
