-- 服务监控系统数据库初始化脚本
-- 创建服务监控相关的数据表

-- 1. 核心服务信息表
CREATE TABLE IF NOT EXISTS `service_info` (
    `service_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `service_type` varchar(20) NOT NULL COMMENT '服务类型(system/application)',
    `display_name` varchar(100) NOT NULL COMMENT '显示名称',
    `description` varchar(500) DEFAULT NULL COMMENT '服务描述',
    `host` varchar(100) DEFAULT '127.0.0.1' COMMENT '服务主机',
    `port` int(11) DEFAULT NULL COMMENT '服务端口',
    `health_check_url` varchar(200) DEFAULT NULL COMMENT '健康检查URL',
    `health_check_interval` int(11) DEFAULT 30 COMMENT '健康检查间隔(秒)',
    `is_critical` tinyint(1) DEFAULT 1 COMMENT '是否为关键服务',
    `auto_restart` tinyint(1) DEFAULT 1 COMMENT '是否自动重启',
    `max_restart_attempts` int(11) DEFAULT 3 COMMENT '最大重启尝试次数',
    `dependencies` json DEFAULT NULL COMMENT '服务依赖关系',
    `config_params` json DEFAULT NULL COMMENT '服务配置参数',
    `status` varchar(20) DEFAULT 'active' COMMENT '服务状态(active/inactive)',
    `created_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT 'system' COMMENT '更新者',
    `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`service_id`),
    UNIQUE KEY `uk_service_name` (`service_name`),
    KEY `idx_service_type` (`service_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核心服务信息表';

-- 2. 服务状态记录表
CREATE TABLE IF NOT EXISTS `service_status` (
    `status_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '状态记录ID',
    `service_id` int(11) NOT NULL COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `status` varchar(20) NOT NULL COMMENT '服务状态(running/stopped/error/starting/stopping)',
    `health_status` varchar(20) DEFAULT NULL COMMENT '健康状态(healthy/unhealthy/unknown)',
    `pid` int(11) DEFAULT NULL COMMENT '进程ID',
    `cpu_usage` decimal(5,2) DEFAULT NULL COMMENT 'CPU使用率(%)',
    `memory_usage` decimal(5,2) DEFAULT NULL COMMENT '内存使用率(%)',
    `memory_used_mb` decimal(10,2) DEFAULT NULL COMMENT '内存使用量(MB)',
    `disk_usage` decimal(5,2) DEFAULT NULL COMMENT '磁盘使用率(%)',
    `network_connections` int(11) DEFAULT NULL COMMENT '网络连接数',
    `uptime_seconds` bigint(20) DEFAULT NULL COMMENT '运行时间(秒)',
    `response_time_ms` decimal(10,2) DEFAULT NULL COMMENT '响应时间(毫秒)',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `last_check_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最后检查时间',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`status_id`),
    KEY `idx_service_id` (`service_id`),
    KEY `idx_service_name` (`service_name`),
    KEY `idx_status` (`status`),
    KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务状态记录表';

-- 3. 服务健康检查记录表
CREATE TABLE IF NOT EXISTS `service_health_check` (
    `check_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '检查记录ID',
    `service_id` int(11) NOT NULL COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `check_type` varchar(20) NOT NULL COMMENT '检查类型(process/port/http/custom)',
    `check_result` varchar(20) NOT NULL COMMENT '检查结果(success/failed/timeout)',
    `response_time_ms` decimal(10,2) DEFAULT NULL COMMENT '响应时间(毫秒)',
    `status_code` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
    `response_body` text DEFAULT NULL COMMENT '响应内容',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `check_details` json DEFAULT NULL COMMENT '检查详细信息',
    `check_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
    PRIMARY KEY (`check_id`),
    KEY `idx_service_id` (`service_id`),
    KEY `idx_service_name` (`service_name`),
    KEY `idx_check_result` (`check_result`),
    KEY `idx_check_time` (`check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务健康检查记录表';

-- 4. 服务性能指标表
CREATE TABLE IF NOT EXISTS `service_performance_metrics` (
    `metric_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '指标记录ID',
    `service_id` int(11) NOT NULL COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
    `metric_type` varchar(20) NOT NULL COMMENT '指标类型(counter/gauge/histogram)',
    `metric_value` decimal(15,4) NOT NULL COMMENT '指标值',
    `metric_unit` varchar(20) DEFAULT NULL COMMENT '指标单位',
    `labels` json DEFAULT NULL COMMENT '指标标签',
    `timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '指标时间戳',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`metric_id`),
    KEY `idx_service_id` (`service_id`),
    KEY `idx_service_name` (`service_name`),
    KEY `idx_metric_name` (`metric_name`),
    KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务性能指标表';

-- 5. 服务告警记录表
CREATE TABLE IF NOT EXISTS `service_alert` (
    `alert_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '告警ID',
    `service_id` int(11) NOT NULL COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `alert_type` varchar(50) NOT NULL COMMENT '告警类型',
    `alert_level` varchar(20) NOT NULL COMMENT '告警级别(info/warning/error/critical)',
    `alert_title` varchar(200) NOT NULL COMMENT '告警标题',
    `alert_message` text NOT NULL COMMENT '告警消息',
    `alert_details` json DEFAULT NULL COMMENT '告警详细信息',
    `threshold_value` decimal(15,4) DEFAULT NULL COMMENT '阈值',
    `current_value` decimal(15,4) DEFAULT NULL COMMENT '当前值',
    `is_resolved` tinyint(1) DEFAULT 0 COMMENT '是否已解决',
    `resolved_time` datetime DEFAULT NULL COMMENT '解决时间',
    `resolved_by` varchar(64) DEFAULT NULL COMMENT '解决人',
    `resolution_notes` text DEFAULT NULL COMMENT '解决备注',
    `alert_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '告警时间',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`alert_id`),
    KEY `idx_service_id` (`service_id`),
    KEY `idx_service_name` (`service_name`),
    KEY `idx_alert_level` (`alert_level`),
    KEY `idx_is_resolved` (`is_resolved`),
    KEY `idx_alert_time` (`alert_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务告警记录表';

-- 6. 服务日志记录表
CREATE TABLE IF NOT EXISTS `service_log` (
    `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `service_id` int(11) NOT NULL COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `log_level` varchar(20) NOT NULL COMMENT '日志级别(DEBUG/INFO/WARNING/ERROR/CRITICAL)',
    `log_source` varchar(100) DEFAULT NULL COMMENT '日志来源',
    `log_message` text NOT NULL COMMENT '日志消息',
    `log_details` json DEFAULT NULL COMMENT '日志详细信息',
    `exception_info` text DEFAULT NULL COMMENT '异常信息',
    `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
    `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `log_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '日志时间',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`log_id`),
    KEY `idx_service_id` (`service_id`),
    KEY `idx_service_name` (`service_name`),
    KEY `idx_log_level` (`log_level`),
    KEY `idx_log_time` (`log_time`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务日志记录表';

-- 7. 服务操作记录表
CREATE TABLE IF NOT EXISTS `service_operation` (
    `operation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '操作ID',
    `service_id` int(11) NOT NULL COMMENT '服务ID',
    `service_name` varchar(50) NOT NULL COMMENT '服务名称',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型(start/stop/restart/config/deploy)',
    `operation_status` varchar(20) NOT NULL COMMENT '操作状态(pending/running/success/failed)',
    `operation_params` json DEFAULT NULL COMMENT '操作参数',
    `operation_result` text DEFAULT NULL COMMENT '操作结果',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `execution_time_ms` int(11) DEFAULT NULL COMMENT '执行时间(毫秒)',
    `operator` varchar(64) NOT NULL COMMENT '操作人',
    `operator_ip` varchar(50) DEFAULT NULL COMMENT '操作人IP',
    `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    PRIMARY KEY (`operation_id`),
    KEY `idx_service_id` (`service_id`),
    KEY `idx_service_name` (`service_name`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_operation_status` (`operation_status`),
    KEY `idx_operator` (`operator`),
    KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务操作记录表';

-- 插入核心服务初始数据
INSERT INTO `service_info` (`service_name`, `service_type`, `display_name`, `description`, `host`, `port`, `health_check_url`, `is_critical`, `config_params`) VALUES
('mysql', 'system', 'MySQL数据库', 'MySQL数据库服务', '127.0.0.1', 3306, NULL, 1, '{"health_check_type": "port"}'),
('redis', 'system', 'Redis缓存', 'Redis缓存服务', '127.0.0.1', 6379, NULL, 1, '{"health_check_type": "port"}'),
('backend', 'application', '后端API服务', 'FastAPI后端服务', '127.0.0.1', 9099, 'http://127.0.0.1:9099/health', 1, '{"health_check_type": "http"}'),
('frontend', 'application', '前端Web服务', 'Dash前端服务', '127.0.0.1', 8089, 'http://127.0.0.1:8089/', 1, '{"health_check_type": "http"}'),
('location', 'application', '归属地服务', '电话号码归属地查询服务', '127.0.0.1', 8001, 'http://127.0.0.1:8001/health', 0, '{"health_check_type": "http"}'),
('nlp', 'application', 'NLP处理服务', '自然语言处理服务', '127.0.0.1', 8002, 'http://127.0.0.1:8002/health', 0, '{"health_check_type": "http"}'),
('batch', 'application', '批处理服务', '批量数据处理服务', '127.0.0.1', 8003, 'http://127.0.0.1:8003/health', 0, '{"health_check_type": "http"}'),
('gateway', 'application', 'API网关', 'API网关服务', '127.0.0.1', 8000, 'http://127.0.0.1:8000/health', 1, '{"health_check_type": "http"}')
ON DUPLICATE KEY UPDATE
    `display_name` = VALUES(`display_name`),
    `description` = VALUES(`description`),
    `host` = VALUES(`host`),
    `port` = VALUES(`port`),
    `health_check_url` = VALUES(`health_check_url`),
    `is_critical` = VALUES(`is_critical`),
    `config_params` = VALUES(`config_params`),
    `updated_time` = CURRENT_TIMESTAMP;

-- 创建索引优化查询性能
-- 服务状态表按时间分区索引
ALTER TABLE `service_status` ADD INDEX `idx_service_time` (`service_name`, `created_time`);

-- 健康检查表按时间分区索引
ALTER TABLE `service_health_check` ADD INDEX `idx_service_check_time` (`service_name`, `check_time`);

-- 性能指标表按服务和时间索引
ALTER TABLE `service_performance_metrics` ADD INDEX `idx_service_metric_time` (`service_name`, `metric_name`, `timestamp`);

-- 告警表按服务和时间索引
ALTER TABLE `service_alert` ADD INDEX `idx_service_alert_time` (`service_name`, `alert_time`);

-- 日志表按服务和时间索引
ALTER TABLE `service_log` ADD INDEX `idx_service_log_time` (`service_name`, `log_time`);

-- 操作记录表按服务和时间索引
ALTER TABLE `service_operation` ADD INDEX `idx_service_operation_time` (`service_name`, `operation_time`);
