-- 核心服务监控菜单配置脚本
-- 添加核心服务监控相关的菜单项

-- 1. 添加核心服务监控主菜单（父菜单）
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '核心服务监控', 0, 6, 'core-service-monitor', NULL, NULL,
    1, 0, 'M', '0', '0', NULL,
    'monitor', 'admin', NOW(), 'admin', NOW(), '核心服务监控管理'
);

-- 获取刚插入的父菜单ID（假设为500，实际会根据数据库自增）
SET @parent_menu_id = LAST_INSERT_ID();

-- 2. 添加监控仪表板菜单
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '监控仪表板', @parent_menu_id, 1, 'service-dashboard', 'service_monitor/service_dashboard', NULL,
    1, 0, 'C', '0', '0', 'monitor:service:query',
    'dashboard', 'admin', NOW(), 'admin', NOW(), '服务监控仪表板'
);

-- 3. 添加服务管理菜单
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '服务管理', @parent_menu_id, 2, 'service-list', 'service_monitor/service_list', NULL,
    1, 0, 'C', '0', '0', 'monitor:service:query',
    'cluster', 'admin', NOW(), 'admin', NOW(), '核心服务管理'
);

-- 4. 添加服务日志菜单
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '服务日志', @parent_menu_id, 3, 'service-logs', 'service_monitor/service_logs', NULL,
    1, 0, 'C', '0', '0', 'monitor:log:query',
    'file-text', 'admin', NOW(), 'admin', NOW(), '服务日志查看'
);

-- 5. 添加告警管理菜单
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '告警管理', @parent_menu_id, 4, 'service-alerts', 'service_monitor/service_alerts', NULL,
    1, 0, 'C', '0', '0', 'monitor:alert:query',
    'alert', 'admin', NOW(), 'admin', NOW(), '服务告警管理'
);

-- 6. 添加操作记录菜单
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '操作记录', @parent_menu_id, 5, 'service-operations', 'service_monitor/service_operations', NULL,
    1, 0, 'C', '0', '0', 'monitor:operation:query',
    'history', 'admin', NOW(), 'admin', NOW(), '服务操作记录'
);

-- 7. 添加性能监控菜单
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '性能监控', @parent_menu_id, 6, 'service-performance', 'service_monitor/service_performance', NULL,
    1, 0, 'C', '0', '0', 'monitor:service:query',
    'line-chart', 'admin', NOW(), 'admin', NOW(), '服务性能监控'
);

-- 8. 添加服务管理相关的按钮权限
-- 服务初始化权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '服务初始化', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '服务管理' AND parent_id = @parent_menu_id) AS temp), 1, '#', '', NULL,
    1, 0, 'F', '0', '0', 'monitor:service:init',
    '#', 'admin', NOW(), 'admin', NOW(), '初始化核心服务'
);

-- 服务启动权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '服务启动', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '服务管理' AND parent_id = @parent_menu_id) AS temp), 2, '#', '', NULL,
    1, 0, 'F', '0', '0', 'monitor:service:start',
    '#', 'admin', NOW(), 'admin', NOW(), '启动服务'
);

-- 服务停止权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '服务停止', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '服务管理' AND parent_id = @parent_menu_id) AS temp), 3, '#', '', NULL,
    1, 0, 'F', '0', '0', 'monitor:service:stop',
    '#', 'admin', NOW(), 'admin', NOW(), '停止服务'
);

-- 服务重启权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '服务重启', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '服务管理' AND parent_id = @parent_menu_id) AS temp), 4, '#', '', NULL,
    1, 0, 'F', '0', '0', 'monitor:service:restart',
    '#', 'admin', NOW(), 'admin', NOW(), '重启服务'
);

-- 健康检查权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '健康检查', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '服务管理' AND parent_id = @parent_menu_id) AS temp), 5, '#', '', NULL,
    1, 0, 'F', '0', '0', 'monitor:service:check',
    '#', 'admin', NOW(), 'admin', NOW(), '执行健康检查'
);

-- 9. 添加告警管理相关的按钮权限
-- 告警解决权限
INSERT INTO `sys_menu` (
    `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`,
    `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`,
    `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`
) VALUES (
    '告警解决', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '告警管理' AND parent_id = @parent_menu_id) AS temp), 1, '#', '', NULL,
    1, 0, 'F', '0', '0', 'monitor:alert:resolve',
    '#', 'admin', NOW(), 'admin', NOW(), '解决告警'
);

-- 10. 为admin角色分配所有核心服务监控权限
-- 获取admin角色ID
SET @admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'admin' LIMIT 1);

-- 为admin角色分配核心服务监控菜单权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`)
SELECT @admin_role_id, menu_id 
FROM `sys_menu` 
WHERE menu_name IN ('核心服务监控', '监控仪表板', '服务管理', '服务日志', '告警管理', '操作记录', '性能监控')
   OR perms LIKE 'monitor:%'
ON DUPLICATE KEY UPDATE role_id = role_id;

-- 显示创建的菜单
SELECT 
    menu_id,
    menu_name,
    path,
    component,
    perms,
    CASE 
        WHEN parent_id = 0 THEN '根菜单'
        ELSE (SELECT menu_name FROM sys_menu p WHERE p.menu_id = m.parent_id)
    END as parent_name
FROM sys_menu m 
WHERE menu_name LIKE '%核心服务监控%' 
   OR menu_name IN ('监控仪表板', '服务管理', '服务日志', '告警管理', '操作记录', '性能监控')
   OR perms LIKE 'monitor:%'
ORDER BY parent_id, order_num;
