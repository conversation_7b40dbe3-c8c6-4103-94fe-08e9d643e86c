"""
电话标记识别管理系统 - 专用应用入口
基于 Dash-FastAPI-Admin 框架构建的完整管理系统
"""
import dash
from dash import html, dcc, Input, Output, State, callback
import feffery_antd_components as fac
import feffery_utils_components as fuc
from flask import session
import time

# 导入电话标记系统的视图组件
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dash-fastapi-frontend'))

from views.phone_system import (
    render_phone_dashboard,
    render_batch_import,
    render_device_management,
    render_data_statistics
)

# 导入服务监控系统的视图组件
from views.service_monitor import (
    render_service_dashboard,
    render_service_list,
    render_service_logs,
    render_service_alerts,
    render_service_operations
)

# 创建Dash应用
app = dash.Dash(
    __name__,
    compress=True,
    suppress_callback_exceptions=True,
    update_title=None,
    title='电话标记识别管理系统'
)

# 配置服务器
server = app.server
app.server.secret_key = 'phone_marking_system_secret_key_2025'

# 应用布局
app.layout = html.Div([
    # URL监听组件
    dcc.Location(id='url', refresh=False),
    
    # 主布局容器
    fac.AntdConfigProvider(
        locale='zh-cn',
        children=[
            # 顶部导航栏
            fac.AntdAffix([
                fac.AntdHeader([
                    fac.AntdRow([
                        fac.AntdCol([
                            fac.AntdSpace([
                                fac.AntdIcon(
                                    icon='antd-phone',
                                    style={'fontSize': '24px', 'color': '#1890ff'}
                                ),
                                fac.AntdTitle(
                                    '电话标记识别管理系统',
                                    level=3,
                                    style={'margin': 0, 'color': 'white'}
                                )
                            ])
                        ], span=12),
                        
                        fac.AntdCol([
                            fac.AntdSpace([
                                fac.AntdBadge([
                                    fac.AntdButton(
                                        icon=fac.AntdIcon(icon='antd-bell'),
                                        type='text',
                                        style={'color': 'white'}
                                    )
                                ], count=3, size='small'),
                                
                                fac.AntdButton([
                                    fac.AntdAvatar(
                                        mode='text',
                                        text='管理员',
                                        size='small',
                                        style={'marginRight': '8px'}
                                    ),
                                    '管理员'
                                ], type='text', style={'color': 'white'})
                            ], style={'float': 'right'})
                        ], span=12)
                    ])
                ], style={
                    'background': '#001529',
                    'padding': '0 24px',
                    'height': '64px',
                    'lineHeight': '64px'
                })
            ], offsetTop=0),
            
            # 主内容区域
            fac.AntdLayout([
                # 侧边栏
                fac.AntdSider([
                    fac.AntdMenu(
                        id='main-menu',
                        menuItems=[
                            {
                                'component': 'Item',
                                'props': {
                                    'key': 'dashboard',
                                    'icon': 'antd-dashboard',
                                    'title': '系统概览'
                                }
                            },
                            {
                                'component': 'SubMenu',
                                'props': {
                                    'key': 'data-management',
                                    'icon': 'antd-database',
                                    'title': '数据管理'
                                },
                                'children': [
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'batch-import',
                                            'title': '批量导入'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'data-export',
                                            'title': '数据导出'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'data-sync',
                                            'title': '数据同步'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'Item',
                                'props': {
                                    'key': 'device-management',
                                    'icon': 'antd-mobile',
                                    'title': '设备管理'
                                }
                            },
                            {
                                'component': 'Item',
                                'props': {
                                    'key': 'data-statistics',
                                    'icon': 'antd-bar-chart',
                                    'title': '数据统计'
                                }
                            },
                            {
                                'component': 'SubMenu',
                                'props': {
                                    'key': 'task-management',
                                    'icon': 'antd-schedule',
                                    'title': '任务管理'
                                },
                                'children': [
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'task-monitor',
                                            'title': '任务监控'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'task-history',
                                            'title': '历史任务'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'SubMenu',
                                'props': {
                                    'key': 'service-monitor',
                                    'icon': 'antd-monitor',
                                    'title': '服务监控'
                                },
                                'children': [
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'service-dashboard',
                                            'title': '监控仪表板'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'service-list',
                                            'title': '服务管理'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'service-logs',
                                            'title': '服务日志'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'service-alerts',
                                            'title': '告警管理'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'service-operations',
                                            'title': '操作记录'
                                        }
                                    }
                                ]
                            },
                            {
                                'component': 'SubMenu',
                                'props': {
                                    'key': 'system-management',
                                    'icon': 'antd-setting',
                                    'title': '系统管理'
                                },
                                'children': [
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'user-management',
                                            'title': '用户管理'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'role-management',
                                            'title': '角色管理'
                                        }
                                    },
                                    {
                                        'component': 'Item',
                                        'props': {
                                            'key': 'system-logs',
                                            'title': '系统日志'
                                        }
                                    }
                                ]
                            }
                        ],
                        mode='inline',
                        defaultSelectedKey='dashboard',
                        theme='dark',
                        style={'height': '100%', 'borderRight': 0}
                    )
                ], 
                width=256,
                theme='dark',
                style={'minHeight': 'calc(100vh - 64px)'}),
                
                # 内容区域
                fac.AntdContent([
                    html.Div(
                        id='page-content',
                        style={
                            'padding': '24px',
                            'minHeight': 'calc(100vh - 64px)',
                            'background': '#f0f2f5'
                        }
                    )
                ])
            ])
        ]
    ),
    
    # 全局消息提示容器
    html.Div(id='global-message-container')
])


# 页面路由回调
@callback(
    Output('page-content', 'children'),
    [Input('url', 'pathname'),
     Input('main-menu', 'currentKey')]
)
def display_page(pathname, current_key):
    """
    根据URL路径或菜单选择显示对应页面
    """
    # 根据菜单选择或URL路径确定要显示的页面
    page_key = current_key or 'dashboard'
    
    if pathname:
        # 从URL路径提取页面标识
        if pathname.endswith('/batch-import'):
            page_key = 'batch-import'
        elif pathname.endswith('/device-management'):
            page_key = 'device-management'
        elif pathname.endswith('/data-statistics'):
            page_key = 'data-statistics'
        elif pathname.endswith('/service-dashboard'):
            page_key = 'service-dashboard'
        elif pathname.endswith('/service-list'):
            page_key = 'service-list'
        elif pathname.endswith('/service-logs'):
            page_key = 'service-logs'
        elif pathname.endswith('/service-alerts'):
            page_key = 'service-alerts'
        elif pathname.endswith('/service-operations'):
            page_key = 'service-operations'
        elif pathname.endswith('/dashboard') or pathname == '/':
            page_key = 'dashboard'
    
    # 根据页面标识返回对应的页面内容
    if page_key == 'dashboard':
        return render_phone_dashboard()
    elif page_key == 'batch-import':
        return render_batch_import()
    elif page_key == 'device-management':
        return render_device_management()
    elif page_key == 'data-statistics':
        return render_data_statistics()
    elif page_key == 'service-dashboard':
        return render_service_dashboard()
    elif page_key == 'service-list':
        return render_service_list()
    elif page_key == 'service-logs':
        return render_service_logs()
    elif page_key == 'service-alerts':
        return render_service_alerts()
    elif page_key == 'service-operations':
        return render_service_operations()
    elif page_key == 'data-export':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='数据导出',
                subTitle='数据导出功能开发中...'
            )
        ])
    elif page_key == 'data-sync':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='数据同步',
                subTitle='数据同步功能开发中...'
            )
        ])
    elif page_key == 'task-monitor':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='任务监控',
                subTitle='任务监控功能开发中...'
            )
        ])
    elif page_key == 'task-history':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='历史任务',
                subTitle='历史任务功能开发中...'
            )
        ])
    elif page_key == 'user-management':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='用户管理',
                subTitle='用户管理功能开发中...'
            )
        ])
    elif page_key == 'role-management':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='角色管理',
                subTitle='角色管理功能开发中...'
            )
        ])
    elif page_key == 'system-logs':
        return html.Div([
            fac.AntdResult(
                status='info',
                title='系统日志',
                subTitle='系统日志功能开发中...'
            )
        ])
    else:
        return html.Div([
            fac.AntdResult(
                status='404',
                title='页面未找到',
                subTitle='抱歉，您访问的页面不存在。'
            )
        ])


# 菜单点击回调
@callback(
    Output('url', 'pathname'),
    [Input('main-menu', 'currentKey')],
    prevent_initial_call=True
)
def update_url(current_key):
    """
    根据菜单选择更新URL
    """
    if current_key:
        return f'/{current_key}'
    return '/'


if __name__ == '__main__':
    app.run(
        debug=True,
        host='127.0.0.1',
        port=8089
    )
