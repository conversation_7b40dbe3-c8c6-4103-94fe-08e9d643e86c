"""
服务性能监控页面
显示服务的性能指标图表和趋势分析
"""
import dash
from dash import html, dcc, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
import plotly.graph_objs as go
import plotly.express as px
from datetime import datetime, timedelta
import requests
import json

from config.global_config import ApiBaseUrlConfig
from utils.common_util import validate_data_not_empty


def render_service_performance():
    """渲染服务性能监控页面"""
    return html.Div([
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdIcon(icon='antd-line-chart', style={'fontSize': '24px', 'color': '#1890ff'}),
                    fac.AntdTitle('性能监控', level=2, style={'margin': 0})
                ])
            ], span=12),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '刷新数据',
                        id='refresh-performance-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-reload')
                    ),
                    fac.AntdButton(
                        '导出报告',
                        id='export-performance-btn',
                        type='default',
                        icon=fac.AntdIcon(icon='antd-download')
                    )
                ], style={'float': 'right'})
            ], span=12)
        ], style={'marginBottom': '24px'}),
        
        # 性能监控控制面板
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSelect(
                        id='performance-service-select',
                        placeholder='选择服务',
                        options=[
                            {'label': 'MySQL数据库', 'value': 'mysql'},
                            {'label': 'Redis缓存', 'value': 'redis'},
                            {'label': '后端API服务', 'value': 'backend'},
                            {'label': '前端Web服务', 'value': 'frontend'},
                            {'label': '归属地服务', 'value': 'location'},
                            {'label': 'NLP处理服务', 'value': 'nlp'},
                            {'label': '批处理服务', 'value': 'batch'},
                            {'label': 'API网关', 'value': 'gateway'}
                        ],
                        value='backend',
                        style={'width': '100%'}
                    )
                ], span=6),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='performance-time-range-select',
                        placeholder='选择时间范围',
                        options=[
                            {'label': '最近1小时', 'value': '1h'},
                            {'label': '最近6小时', 'value': '6h'},
                            {'label': '最近24小时', 'value': '24h'},
                            {'label': '最近7天', 'value': '7d'}
                        ],
                        value='24h',
                        style={'width': '100%'}
                    )
                ], span=6),
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '查询',
                            id='query-performance-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-search')
                        ),
                        fac.AntdSwitch(
                            id='auto-refresh-switch',
                            checkedChildren='自动刷新',
                            unCheckedChildren='手动刷新',
                            checked=False
                        )
                    ])
                ], span=12)
            ], gutter=16)
        ], style={'marginBottom': '24px'}),
        
        # 性能指标概览
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='当前CPU使用率',
                        value=0,
                        suffix='%',
                        id='current-cpu-stat',
                        valueStyle={'color': '#1890ff'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='当前内存使用率',
                        value=0,
                        suffix='%',
                        id='current-memory-stat',
                        valueStyle={'color': '#722ed1'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='平均响应时间',
                        value=0,
                        suffix='ms',
                        id='avg-response-time-stat',
                        valueStyle={'color': '#52c41a'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='网络连接数',
                        value=0,
                        id='network-connections-stat',
                        valueStyle={'color': '#faad14'}
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': '24px'}),
        
        # 性能图表
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTitle('CPU使用率趋势', level=4),
                    fuc.FefferyLoading(
                        id='cpu-chart-loading',
                        children=[
                            dcc.Graph(
                                id='cpu-usage-chart',
                                config={'displayModeBar': False},
                                style={'height': '300px'}
                            )
                        ]
                    )
                ], title='CPU监控')
            ], span=12),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTitle('内存使用率趋势', level=4),
                    fuc.FefferyLoading(
                        id='memory-chart-loading',
                        children=[
                            dcc.Graph(
                                id='memory-usage-chart',
                                config={'displayModeBar': False},
                                style={'height': '300px'}
                            )
                        ]
                    )
                ], title='内存监控')
            ], span=12)
        ], gutter=16, style={'marginBottom': '24px'}),
        
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTitle('响应时间趋势', level=4),
                    fuc.FefferyLoading(
                        id='response-time-chart-loading',
                        children=[
                            dcc.Graph(
                                id='response-time-chart',
                                config={'displayModeBar': False},
                                style={'height': '300px'}
                            )
                        ]
                    )
                ], title='响应时间监控')
            ], span=12),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdTitle('网络连接趋势', level=4),
                    fuc.FefferyLoading(
                        id='network-chart-loading',
                        children=[
                            dcc.Graph(
                                id='network-connections-chart',
                                config={'displayModeBar': False},
                                style={'height': '300px'}
                            )
                        ]
                    )
                ], title='网络监控')
            ], span=12)
        ], gutter=16, style={'marginBottom': '24px'}),
        
        # 性能分析报告
        fac.AntdCard([
            fac.AntdTitle('性能分析报告', level=4),
            html.Div(id='performance-analysis-content')
        ], title='智能分析'),
        
        # 隐藏的数据存储组件
        dcc.Store(id='performance-data-store'),
        dcc.Interval(
            id='performance-interval',
            interval=30*1000,  # 30秒刷新一次
            n_intervals=0,
            disabled=True
        ),
        
        # 消息提示容器
        html.Div(id='performance-message-container')
    ])


@callback(
    Output('performance-interval', 'disabled'),
    [Input('auto-refresh-switch', 'checked')],
    prevent_initial_call=True
)
def toggle_auto_refresh(auto_refresh):
    """切换自动刷新"""
    return not auto_refresh


@callback(
    [Output('performance-data-store', 'data'),
     Output('cpu-chart-loading', 'loading'),
     Output('memory-chart-loading', 'loading'),
     Output('response-time-chart-loading', 'loading'),
     Output('network-chart-loading', 'loading')],
    [Input('refresh-performance-btn', 'nClicks'),
     Input('query-performance-btn', 'nClicks'),
     Input('performance-interval', 'n_intervals')],
    [State('performance-service-select', 'value'),
     State('performance-time-range-select', 'value')],
    prevent_initial_call=False
)
def load_performance_data(refresh_clicks, query_clicks, n_intervals, service_name, time_range):
    """加载性能数据"""
    if not service_name:
        return {}, False, False, False, False
    
    try:
        # 调用API获取性能图表数据
        response = requests.get(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/services/{service_name}/performance",
            params={'time_range': time_range or '24h'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}), False, False, False, False
            else:
                return {}, False, False, False, False
        else:
            return {}, False, False, False, False
            
    except Exception as e:
        print(f"加载性能数据失败: {str(e)}")
        return {}, False, False, False, False


@callback(
    [Output('current-cpu-stat', 'value'),
     Output('current-memory-stat', 'value'),
     Output('avg-response-time-stat', 'value'),
     Output('network-connections-stat', 'value'),
     Output('cpu-usage-chart', 'figure'),
     Output('memory-usage-chart', 'figure'),
     Output('response-time-chart', 'figure'),
     Output('network-connections-chart', 'figure'),
     Output('performance-analysis-content', 'children')],
    [Input('performance-data-store', 'data')],
    prevent_initial_call=True
)
def update_performance_display(performance_data):
    """更新性能显示"""
    if not performance_data:
        empty_fig = go.Figure()
        empty_fig.update_layout(
            title="暂无数据",
            xaxis_title="时间",
            yaxis_title="值",
            height=300
        )
        return 0, 0, 0, 0, empty_fig, empty_fig, empty_fig, empty_fig, "暂无数据"
    
    try:
        # 获取最新的统计值
        cpu_data = performance_data.get('cpuData', [])
        memory_data = performance_data.get('memoryData', [])
        response_time_data = performance_data.get('responseTimeData', [])
        network_data = performance_data.get('networkData', [])
        
        current_cpu = cpu_data[-1]['value'] if cpu_data else 0
        current_memory = memory_data[-1]['value'] if memory_data else 0
        avg_response_time = sum(d['value'] for d in response_time_data) / len(response_time_data) if response_time_data else 0
        current_connections = network_data[-1]['value'] if network_data else 0
        
        # 创建CPU使用率图表
        cpu_fig = go.Figure()
        if cpu_data:
            cpu_fig.add_trace(go.Scatter(
                x=[d['time'] for d in cpu_data],
                y=[d['value'] for d in cpu_data],
                mode='lines+markers',
                name='CPU使用率',
                line=dict(color='#1890ff', width=2),
                marker=dict(size=4)
            ))
        cpu_fig.update_layout(
            title="CPU使用率趋势",
            xaxis_title="时间",
            yaxis_title="使用率 (%)",
            height=300,
            showlegend=False
        )
        
        # 创建内存使用率图表
        memory_fig = go.Figure()
        if memory_data:
            memory_fig.add_trace(go.Scatter(
                x=[d['time'] for d in memory_data],
                y=[d['value'] for d in memory_data],
                mode='lines+markers',
                name='内存使用率',
                line=dict(color='#722ed1', width=2),
                marker=dict(size=4)
            ))
        memory_fig.update_layout(
            title="内存使用率趋势",
            xaxis_title="时间",
            yaxis_title="使用率 (%)",
            height=300,
            showlegend=False
        )
        
        # 创建响应时间图表
        response_fig = go.Figure()
        if response_time_data:
            response_fig.add_trace(go.Scatter(
                x=[d['time'] for d in response_time_data],
                y=[d['value'] for d in response_time_data],
                mode='lines+markers',
                name='响应时间',
                line=dict(color='#52c41a', width=2),
                marker=dict(size=4)
            ))
        response_fig.update_layout(
            title="响应时间趋势",
            xaxis_title="时间",
            yaxis_title="响应时间 (ms)",
            height=300,
            showlegend=False
        )
        
        # 创建网络连接图表
        network_fig = go.Figure()
        if network_data:
            network_fig.add_trace(go.Scatter(
                x=[d['time'] for d in network_data],
                y=[d['value'] for d in network_data],
                mode='lines+markers',
                name='网络连接数',
                line=dict(color='#faad14', width=2),
                marker=dict(size=4)
            ))
        network_fig.update_layout(
            title="网络连接趋势",
            xaxis_title="时间",
            yaxis_title="连接数",
            height=300,
            showlegend=False
        )
        
        # 生成性能分析报告
        analysis_content = generate_performance_analysis(
            current_cpu, current_memory, avg_response_time, current_connections
        )
        
        return (
            round(current_cpu, 1), round(current_memory, 1), 
            round(avg_response_time, 1), current_connections,
            cpu_fig, memory_fig, response_fig, network_fig,
            analysis_content
        )
        
    except Exception as e:
        print(f"更新性能显示失败: {str(e)}")
        empty_fig = go.Figure()
        empty_fig.update_layout(title="数据加载失败", height=300)
        return 0, 0, 0, 0, empty_fig, empty_fig, empty_fig, empty_fig, "数据加载失败"


def generate_performance_analysis(cpu, memory, response_time, connections):
    """生成性能分析报告"""
    analysis_items = []
    
    # CPU分析
    if cpu > 80:
        analysis_items.append(
            fac.AntdAlert(
                message="CPU使用率过高",
                description=f"当前CPU使用率为 {cpu}%，建议检查是否有异常进程或考虑扩容。",
                type="error",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    elif cpu > 60:
        analysis_items.append(
            fac.AntdAlert(
                message="CPU使用率较高",
                description=f"当前CPU使用率为 {cpu}%，需要关注系统负载情况。",
                type="warning",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    else:
        analysis_items.append(
            fac.AntdAlert(
                message="CPU使用率正常",
                description=f"当前CPU使用率为 {cpu}%，系统运行良好。",
                type="success",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    
    # 内存分析
    if memory > 85:
        analysis_items.append(
            fac.AntdAlert(
                message="内存使用率过高",
                description=f"当前内存使用率为 {memory}%，建议释放内存或增加内存容量。",
                type="error",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    elif memory > 70:
        analysis_items.append(
            fac.AntdAlert(
                message="内存使用率较高",
                description=f"当前内存使用率为 {memory}%，建议监控内存使用情况。",
                type="warning",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    else:
        analysis_items.append(
            fac.AntdAlert(
                message="内存使用率正常",
                description=f"当前内存使用率为 {memory}%，内存充足。",
                type="success",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    
    # 响应时间分析
    if response_time > 1000:
        analysis_items.append(
            fac.AntdAlert(
                message="响应时间过长",
                description=f"平均响应时间为 {response_time}ms，建议优化服务性能。",
                type="error",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    elif response_time > 500:
        analysis_items.append(
            fac.AntdAlert(
                message="响应时间较长",
                description=f"平均响应时间为 {response_time}ms，可以考虑优化。",
                type="warning",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    else:
        analysis_items.append(
            fac.AntdAlert(
                message="响应时间良好",
                description=f"平均响应时间为 {response_time}ms，服务响应迅速。",
                type="success",
                showIcon=True,
                style={'marginBottom': '8px'}
            )
        )
    
    return analysis_items


@callback(
    Output('performance-message-container', 'children'),
    [Input('export-performance-btn', 'nClicks')],
    prevent_initial_call=True
)
def export_performance_report(n_clicks):
    """导出性能报告"""
    if not n_clicks:
        return dash.no_update
    
    # 这里可以实现性能报告导出功能
    return fac.AntdMessage(
        content='性能报告导出功能开发中...',
        type='info'
    )
