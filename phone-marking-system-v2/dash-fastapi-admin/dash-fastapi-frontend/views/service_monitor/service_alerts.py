"""
服务告警管理页面
显示和管理服务监控过程中产生的告警信息
"""
import dash
from dash import html, dcc, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import requests
import json

from config.global_config import ApiBaseUrlConfig
from utils.common_util import validate_data_not_empty


def render_service_alerts():
    """渲染服务告警页面"""
    return html.Div([
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdIcon(icon='antd-alert', style={'fontSize': '24px', 'color': '#ff4d4f'}),
                    fac.AntdTitle('告警管理', level=2, style={'margin': 0})
                ])
            ], span=12),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '刷新告警',
                        id='refresh-alerts-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-reload')
                    ),
                    fac.AntdButton(
                        '批量解决',
                        id='batch-resolve-alerts-btn',
                        type='default',
                        icon=fac.AntdIcon(icon='antd-check')
                    )
                ], style={'float': 'right'})
            ], span=12)
        ], style={'marginBottom': '24px'}),
        
        # 告警统计卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总告警数',
                        value=0,
                        id='total-alerts-stat',
                        valueStyle={'color': '#1890ff'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='未解决',
                        value=0,
                        id='unresolved-alerts-stat',
                        valueStyle={'color': '#ff4d4f'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='严重告警',
                        value=0,
                        id='critical-alerts-stat',
                        valueStyle={'color': '#722ed1'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='今日新增',
                        value=0,
                        id='today-alerts-stat',
                        valueStyle={'color': '#faad14'}
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': '24px'}),
        
        # 告警筛选区域
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSelect(
                        id='alert-service-filter',
                        placeholder='选择服务',
                        options=[
                            {'label': '全部服务', 'value': ''},
                            {'label': 'MySQL数据库', 'value': 'mysql'},
                            {'label': 'Redis缓存', 'value': 'redis'},
                            {'label': '后端API服务', 'value': 'backend'},
                            {'label': '前端Web服务', 'value': 'frontend'},
                            {'label': '归属地服务', 'value': 'location'},
                            {'label': 'NLP处理服务', 'value': 'nlp'},
                            {'label': '批处理服务', 'value': 'batch'},
                            {'label': 'API网关', 'value': 'gateway'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=4),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='alert-level-filter',
                        placeholder='选择告警级别',
                        options=[
                            {'label': '全部级别', 'value': ''},
                            {'label': '信息', 'value': 'info'},
                            {'label': '警告', 'value': 'warning'},
                            {'label': '错误', 'value': 'error'},
                            {'label': '严重', 'value': 'critical'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=3),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='alert-status-filter',
                        placeholder='选择解决状态',
                        options=[
                            {'label': '全部状态', 'value': ''},
                            {'label': '未解决', 'value': 'false'},
                            {'label': '已解决', 'value': 'true'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=3),
                fac.AntdCol([
                    fac.AntdRangePicker(
                        id='alert-time-range',
                        placeholder=['开始时间', '结束时间'],
                        showTime=True,
                        format='YYYY-MM-DD HH:mm:ss'
                    )
                ], span=6),
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '搜索',
                            id='search-alerts-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-search')
                        ),
                        fac.AntdButton(
                            '重置',
                            id='reset-alert-search-btn',
                            icon=fac.AntdIcon(icon='antd-redo')
                        )
                    ])
                ], span=8)
            ], gutter=16)
        ], style={'marginBottom': '24px'}),
        
        # 告警列表
        fac.AntdCard([
            fuc.FefferyLoading(
                id='alerts-table-loading',
                children=[
                    fac.AntdTable(
                        id='alerts-table',
                        columns=[
                            {
                                'title': '告警时间',
                                'dataIndex': 'alertTime',
                                'key': 'alertTime',
                                'width': 180,
                                'fixed': 'left'
                            },
                            {
                                'title': '服务名称',
                                'dataIndex': 'serviceName',
                                'key': 'serviceName',
                                'width': 120
                            },
                            {
                                'title': '告警级别',
                                'dataIndex': 'alertLevel',
                                'key': 'alertLevel',
                                'width': 100,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '告警类型',
                                'dataIndex': 'alertType',
                                'key': 'alertType',
                                'width': 150
                            },
                            {
                                'title': '告警标题',
                                'dataIndex': 'alertTitle',
                                'key': 'alertTitle',
                                'ellipsis': True
                            },
                            {
                                'title': '当前值',
                                'dataIndex': 'currentValue',
                                'key': 'currentValue',
                                'width': 100
                            },
                            {
                                'title': '阈值',
                                'dataIndex': 'thresholdValue',
                                'key': 'thresholdValue',
                                'width': 100
                            },
                            {
                                'title': '状态',
                                'dataIndex': 'isResolved',
                                'key': 'isResolved',
                                'width': 100,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '解决人',
                                'dataIndex': 'resolvedBy',
                                'key': 'resolvedBy',
                                'width': 100
                            },
                            {
                                'title': '操作',
                                'dataIndex': 'operation',
                                'key': 'operation',
                                'width': 150,
                                'fixed': 'right',
                                'renderOptions': {'renderType': 'button'}
                            }
                        ],
                        data=[],
                        pagination={
                            'pageSize': 20,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True,
                            'showTotal': True
                        },
                        bordered=True,
                        size='small',
                        scroll={'x': 1400},
                        rowSelection={
                            'type': 'checkbox',
                            'selectedRowKeys': []
                        }
                    )
                ]
            )
        ], title='告警列表'),
        
        # 告警详情模态框
        fac.AntdModal(
            id='alert-detail-modal',
            title='告警详情',
            visible=False,
            width=800,
            children=[
                html.Div(id='alert-detail-content')
            ],
            footer=fac.AntdButton('关闭', id='close-alert-detail-btn', type='primary')
        ),
        
        # 解决告警模态框
        fac.AntdModal(
            id='resolve-alert-modal',
            title='解决告警',
            visible=False,
            children=[
                fac.AntdForm([
                    fac.AntdFormItem([
                        fac.AntdInput(
                            id='resolution-notes-input',
                            placeholder='请输入解决备注（可选）',
                            mode='text-area',
                            autoSize={'minRows': 3, 'maxRows': 6}
                        )
                    ], label='解决备注')
                ])
            ],
            okText='确认解决',
            cancelText='取消'
        ),
        
        # 隐藏的数据存储组件
        dcc.Store(id='alerts-data-store'),
        dcc.Store(id='current-alert-store'),
        dcc.Store(id='selected-alerts-store', data=[]),
        
        # 消息提示容器
        html.Div(id='alerts-message-container')
    ])


@callback(
    [Output('alerts-data-store', 'data'),
     Output('alerts-table-loading', 'loading')],
    [Input('refresh-alerts-btn', 'nClicks'),
     Input('search-alerts-btn', 'nClicks'),
     Input('alerts-table', 'pagination')],
    [State('alert-service-filter', 'value'),
     State('alert-level-filter', 'value'),
     State('alert-status-filter', 'value'),
     State('alert-time-range', 'value')],
    prevent_initial_call=False
)
def load_alerts_data(refresh_clicks, search_clicks, pagination,
                    service_name, alert_level, is_resolved, time_range):
    """加载告警数据"""
    try:
        # 构建查询参数
        params = {}
        if service_name:
            params['service_name'] = service_name
        if alert_level:
            params['alert_level'] = alert_level
        if is_resolved:
            params['is_resolved'] = is_resolved == 'true'
        if time_range and len(time_range) == 2:
            params['start_time'] = time_range[0]
            params['end_time'] = time_range[1]
        
        # 分页参数
        if pagination:
            params['page_num'] = pagination.get('current', 1)
            params['page_size'] = pagination.get('pageSize', 20)
        else:
            params['page_num'] = 1
            params['page_size'] = 20
        
        # 调用API获取告警数据
        response = requests.get(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/alerts",
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}), False
            else:
                return {}, False
        else:
            return {}, False
            
    except Exception as e:
        print(f"加载告警数据失败: {str(e)}")
        return {}, False


@callback(
    [Output('alerts-table', 'data'),
     Output('total-alerts-stat', 'value'),
     Output('unresolved-alerts-stat', 'value'),
     Output('critical-alerts-stat', 'value'),
     Output('today-alerts-stat', 'value')],
    [Input('alerts-data-store', 'data')],
    prevent_initial_call=True
)
def update_alerts_display(alerts_data):
    """更新告警显示"""
    if not alerts_data or 'alerts' not in alerts_data:
        return [], 0, 0, 0, 0
    
    alerts = alerts_data['alerts']
    total_count = alerts_data.get('total', 0)
    
    # 统计告警数据
    unresolved_count = sum(1 for alert in alerts if not alert.get('isResolved', False))
    critical_count = sum(1 for alert in alerts if alert.get('alertLevel') == 'critical')
    
    # 统计今日新增告警
    today = datetime.now().date()
    today_count = 0
    for alert in alerts:
        alert_time = alert.get('alertTime', '')
        if alert_time:
            try:
                alert_date = datetime.fromisoformat(alert_time.replace('Z', '+00:00')).date()
                if alert_date == today:
                    today_count += 1
            except:
                pass
    
    # 构建表格数据
    table_data = []
    for alert in alerts:
        # 告警级别标签
        level = alert.get('alertLevel', 'info')
        level_tag = {
            'info': {'tag': '信息', 'color': 'blue'},
            'warning': {'tag': '警告', 'color': 'orange'},
            'error': {'tag': '错误', 'color': 'red'},
            'critical': {'tag': '严重', 'color': 'purple'}
        }.get(level, {'tag': level, 'color': 'default'})
        
        # 解决状态标签
        is_resolved = alert.get('isResolved', False)
        status_tag = {
            'tag': '已解决' if is_resolved else '未解决',
            'color': 'green' if is_resolved else 'red'
        }
        
        # 操作按钮
        operation_buttons = [
            {
                'content': '详情',
                'type': 'link',
                'size': 'small',
                'custom': f"detail-{alert.get('alertId', '')}"
            }
        ]
        
        if not is_resolved:
            operation_buttons.insert(0, {
                'content': '解决',
                'type': 'primary',
                'size': 'small',
                'custom': f"resolve-{alert.get('alertId', '')}"
            })
        
        table_data.append({
            'key': alert.get('alertId', ''),
            'alertTime': alert.get('alertTime', ''),
            'serviceName': alert.get('serviceName', ''),
            'alertLevel': level_tag,
            'alertType': alert.get('alertType', ''),
            'alertTitle': alert.get('alertTitle', ''),
            'currentValue': alert.get('currentValue', ''),
            'thresholdValue': alert.get('thresholdValue', ''),
            'isResolved': status_tag,
            'resolvedBy': alert.get('resolvedBy', ''),
            'operation': operation_buttons
        })
    
    return table_data, total_count, unresolved_count, critical_count, today_count


@callback(
    [Output('alert-service-filter', 'value'),
     Output('alert-level-filter', 'value'),
     Output('alert-status-filter', 'value'),
     Output('alert-time-range', 'value')],
    [Input('reset-alert-search-btn', 'nClicks')],
    prevent_initial_call=True
)
def reset_alert_search_form(n_clicks):
    """重置告警搜索表单"""
    if n_clicks:
        return '', '', '', None
    return dash.no_update
