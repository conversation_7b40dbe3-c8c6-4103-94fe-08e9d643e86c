"""
服务监控仪表板页面
显示系统核心服务的整体监控状态和关键指标
"""
import dash
from dash import html, dcc, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import requests
import json

from config.global_config import ApiBaseUrlConfig
from utils.common_util import validate_data_not_empty


def render_service_dashboard():
    """渲染服务监控仪表板"""
    return html.Div([
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdIcon(icon='antd-dashboard', style={'fontSize': '24px', 'color': '#1890ff'}),
                    fac.AntdTitle('服务监控仪表板', level=2, style={'margin': 0})
                ])
            ], span=12),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '刷新数据',
                        id='dashboard-refresh-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-reload')
                    ),
                    fac.AntdButton(
                        '执行健康检查',
                        id='dashboard-health-check-btn',
                        type='default',
                        icon=fac.AntdIcon(icon='antd-safety-certificate')
                    )
                ], style={'float': 'right'})
            ], span=12)
        ], style={'marginBottom': '24px'}),
        
        # 数据加载状态
        fuc.FefferyLoading(
            id='dashboard-loading',
            children=[
                # 服务状态概览卡片
                fac.AntdRow([
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdStatistic(
                                title='总服务数',
                                value=0,
                                id='total-services-stat',
                                valueStyle={'color': '#1890ff'}
                            )
                        ])
                    ], span=6),
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdStatistic(
                                title='运行中',
                                value=0,
                                id='running-services-stat',
                                valueStyle={'color': '#52c41a'}
                            )
                        ])
                    ], span=6),
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdStatistic(
                                title='已停止',
                                value=0,
                                id='stopped-services-stat',
                                valueStyle={'color': '#faad14'}
                            )
                        ])
                    ], span=6),
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdStatistic(
                                title='异常',
                                value=0,
                                id='error-services-stat',
                                valueStyle={'color': '#ff4d4f'}
                            )
                        ])
                    ], span=6)
                ], gutter=16, style={'marginBottom': '24px'}),
                
                # 健康状态和性能指标
                fac.AntdRow([
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdTitle('健康状态', level=4),
                            html.Div(id='health-status-content')
                        ], title='服务健康状态')
                    ], span=12),
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdTitle('系统性能', level=4),
                            html.Div(id='system-performance-content')
                        ], title='系统性能指标')
                    ], span=12)
                ], gutter=16, style={'marginBottom': '24px'}),
                
                # 告警信息和最近操作
                fac.AntdRow([
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdTitle('告警信息', level=4),
                            html.Div(id='alerts-summary-content')
                        ], title='告警统计')
                    ], span=12),
                    fac.AntdCol([
                        fac.AntdCard([
                            fac.AntdTitle('最近操作', level=4),
                            html.Div(id='recent-operations-content')
                        ], title='运维操作记录')
                    ], span=12)
                ], gutter=16, style={'marginBottom': '24px'}),
                
                # 服务状态详情表格
                fac.AntdCard([
                    fac.AntdTitle('服务状态详情', level=4),
                    fac.AntdTable(
                        id='services-status-table',
                        columns=[
                            {'title': '服务名称', 'dataIndex': 'serviceName', 'key': 'serviceName', 'width': 120},
                            {'title': '显示名称', 'dataIndex': 'displayName', 'key': 'displayName', 'width': 150},
                            {'title': '服务类型', 'dataIndex': 'serviceType', 'key': 'serviceType', 'width': 100},
                            {'title': '运行状态', 'dataIndex': 'status', 'key': 'status', 'width': 100},
                            {'title': '健康状态', 'dataIndex': 'healthStatus', 'key': 'healthStatus', 'width': 100},
                            {'title': 'CPU使用率', 'dataIndex': 'cpuUsage', 'key': 'cpuUsage', 'width': 100},
                            {'title': '内存使用率', 'dataIndex': 'memoryUsage', 'key': 'memoryUsage', 'width': 100},
                            {'title': '响应时间', 'dataIndex': 'responseTime', 'key': 'responseTime', 'width': 100},
                            {'title': '最后检查', 'dataIndex': 'lastCheckTime', 'key': 'lastCheckTime', 'width': 150},
                            {'title': '操作', 'dataIndex': 'operation', 'key': 'operation', 'width': 200}
                        ],
                        data=[],
                        pagination=False,
                        size='small'
                    )
                ], title='服务状态监控')
            ]
        ),
        
        # 隐藏的数据存储组件
        dcc.Store(id='dashboard-data-store'),
        dcc.Interval(
            id='dashboard-interval',
            interval=30*1000,  # 30秒刷新一次
            n_intervals=0
        ),
        
        # 消息提示容器
        html.Div(id='dashboard-message-container')
    ])


@callback(
    [Output('dashboard-data-store', 'data'),
     Output('dashboard-loading', 'loading')],
    [Input('dashboard-refresh-btn', 'nClicks'),
     Input('dashboard-interval', 'n_intervals')],
    prevent_initial_call=False
)
def refresh_dashboard_data(refresh_clicks, n_intervals):
    """刷新仪表板数据"""
    try:
        # 调用后端API获取仪表板数据
        response = requests.get(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/dashboard",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}), False
            else:
                return {}, False
        else:
            return {}, False
            
    except Exception as e:
        print(f"获取仪表板数据失败: {str(e)}")
        return {}, False


@callback(
    [Output('total-services-stat', 'value'),
     Output('running-services-stat', 'value'),
     Output('stopped-services-stat', 'value'),
     Output('error-services-stat', 'value'),
     Output('health-status-content', 'children'),
     Output('system-performance-content', 'children'),
     Output('alerts-summary-content', 'children'),
     Output('recent-operations-content', 'children'),
     Output('services-status-table', 'data')],
    [Input('dashboard-data-store', 'data')],
    prevent_initial_call=True
)
def update_dashboard_display(dashboard_data):
    """更新仪表板显示内容"""
    if not dashboard_data:
        return 0, 0, 0, 0, "暂无数据", "暂无数据", "暂无数据", "暂无数据", []
    
    try:
        # 更新统计数据
        total_services = dashboard_data.get('totalServices', 0)
        running_services = dashboard_data.get('runningServices', 0)
        stopped_services = dashboard_data.get('stoppedServices', 0)
        error_services = dashboard_data.get('errorServices', 0)
        
        # 健康状态内容
        healthy_services = dashboard_data.get('healthyServices', 0)
        unhealthy_services = dashboard_data.get('unhealthyServices', 0)
        health_content = fac.AntdRow([
            fac.AntdCol([
                fac.AntdStatistic(
                    title='健康',
                    value=healthy_services,
                    valueStyle={'color': '#52c41a'}
                )
            ], span=12),
            fac.AntdCol([
                fac.AntdStatistic(
                    title='不健康',
                    value=unhealthy_services,
                    valueStyle={'color': '#ff4d4f'}
                )
            ], span=12)
        ])
        
        # 系统性能内容
        avg_cpu = dashboard_data.get('avgCpuUsage', 0)
        avg_memory = dashboard_data.get('avgMemoryUsage', 0)
        performance_content = fac.AntdRow([
            fac.AntdCol([
                fac.AntdStatistic(
                    title='平均CPU',
                    value=avg_cpu,
                    suffix='%',
                    valueStyle={'color': '#1890ff'}
                )
            ], span=12),
            fac.AntdCol([
                fac.AntdStatistic(
                    title='平均内存',
                    value=avg_memory,
                    suffix='%',
                    valueStyle={'color': '#722ed1'}
                )
            ], span=12)
        ])
        
        # 告警信息内容
        total_alerts = dashboard_data.get('totalAlerts', 0)
        unresolved_alerts = dashboard_data.get('unresolvedAlerts', 0)
        alerts_content = fac.AntdRow([
            fac.AntdCol([
                fac.AntdStatistic(
                    title='总告警',
                    value=total_alerts,
                    valueStyle={'color': '#faad14'}
                )
            ], span=12),
            fac.AntdCol([
                fac.AntdStatistic(
                    title='未解决',
                    value=unresolved_alerts,
                    valueStyle={'color': '#ff4d4f'}
                )
            ], span=12)
        ])
        
        # 最近操作内容
        recent_operations = dashboard_data.get('recentOperations', [])
        operations_content = []
        for op in recent_operations[:5]:  # 只显示最近5条
            operations_content.append(
                fac.AntdListItem([
                    fac.AntdText(f"{op.get('serviceName', '')} - {op.get('operationType', '')}", strong=True),
                    fac.AntdText(f" ({op.get('operationStatus', '')})", 
                               type='success' if op.get('operationStatus') == 'success' else 'danger'),
                    html.Br(),
                    fac.AntdText(op.get('operationTime', ''), type='secondary', style={'fontSize': '12px'})
                ])
            )
        
        if not operations_content:
            operations_content = [fac.AntdEmpty(description='暂无操作记录')]
        
        operations_list = fac.AntdList(operations_content, size='small')
        
        # 服务状态表格数据
        services_data = []
        # 这里需要从API获取详细的服务状态数据
        # 暂时返回空数据，实际应该调用服务列表API
        
        return (
            total_services, running_services, stopped_services, error_services,
            health_content, performance_content, alerts_content, operations_list,
            services_data
        )
        
    except Exception as e:
        print(f"更新仪表板显示失败: {str(e)}")
        return 0, 0, 0, 0, "数据加载失败", "数据加载失败", "数据加载失败", "数据加载失败", []


@callback(
    Output('dashboard-message-container', 'children'),
    [Input('dashboard-health-check-btn', 'nClicks')],
    prevent_initial_call=True
)
def perform_health_check(n_clicks):
    """执行健康检查"""
    if not n_clicks:
        return dash.no_update
    
    try:
        # 调用健康检查API
        response = requests.post(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/health-check",
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return fac.AntdMessage(
                    content='健康检查执行成功',
                    type='success'
                )
            else:
                return fac.AntdMessage(
                    content=f'健康检查失败: {result.get("message", "未知错误")}',
                    type='error'
                )
        else:
            return fac.AntdMessage(
                content='健康检查请求失败',
                type='error'
            )
            
    except Exception as e:
        return fac.AntdMessage(
            content=f'健康检查执行失败: {str(e)}',
            type='error'
        )
