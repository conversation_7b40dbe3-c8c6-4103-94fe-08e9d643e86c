"""
服务操作记录页面
显示和查询服务运维操作的历史记录
"""
import dash
from dash import html, dcc, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import requests
import json

from config.global_config import ApiBaseUrlConfig
from utils.common_util import validate_data_not_empty


def render_service_operations():
    """渲染服务操作记录页面"""
    return html.Div([
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdIcon(icon='antd-history', style={'fontSize': '24px', 'color': '#1890ff'}),
                    fac.AntdTitle('操作记录', level=2, style={'margin': 0})
                ])
            ], span=12),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '刷新记录',
                        id='refresh-operations-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-reload')
                    ),
                    fac.AntdButton(
                        '导出记录',
                        id='export-operations-btn',
                        type='default',
                        icon=fac.AntdIcon(icon='antd-download')
                    )
                ], style={'float': 'right'})
            ], span=12)
        ], style={'marginBottom': '24px'}),
        
        # 操作统计卡片
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总操作数',
                        value=0,
                        id='total-operations-stat',
                        valueStyle={'color': '#1890ff'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='成功操作',
                        value=0,
                        id='success-operations-stat',
                        valueStyle={'color': '#52c41a'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='失败操作',
                        value=0,
                        id='failed-operations-stat',
                        valueStyle={'color': '#ff4d4f'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='今日操作',
                        value=0,
                        id='today-operations-stat',
                        valueStyle={'color': '#faad14'}
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': '24px'}),
        
        # 操作筛选区域
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSelect(
                        id='operation-service-filter',
                        placeholder='选择服务',
                        options=[
                            {'label': '全部服务', 'value': ''},
                            {'label': 'MySQL数据库', 'value': 'mysql'},
                            {'label': 'Redis缓存', 'value': 'redis'},
                            {'label': '后端API服务', 'value': 'backend'},
                            {'label': '前端Web服务', 'value': 'frontend'},
                            {'label': '归属地服务', 'value': 'location'},
                            {'label': 'NLP处理服务', 'value': 'nlp'},
                            {'label': '批处理服务', 'value': 'batch'},
                            {'label': 'API网关', 'value': 'gateway'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=4),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='operation-type-filter',
                        placeholder='选择操作类型',
                        options=[
                            {'label': '全部类型', 'value': ''},
                            {'label': '启动', 'value': 'start'},
                            {'label': '停止', 'value': 'stop'},
                            {'label': '重启', 'value': 'restart'},
                            {'label': '配置', 'value': 'config'},
                            {'label': '部署', 'value': 'deploy'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=3),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='operation-status-filter',
                        placeholder='选择操作状态',
                        options=[
                            {'label': '全部状态', 'value': ''},
                            {'label': '成功', 'value': 'success'},
                            {'label': '失败', 'value': 'failed'},
                            {'label': '运行中', 'value': 'running'},
                            {'label': '待处理', 'value': 'pending'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=3),
                fac.AntdCol([
                    fac.AntdInput(
                        id='operation-operator-search',
                        placeholder='操作人',
                        prefix=fac.AntdIcon(icon='antd-user'),
                        allowClear=True
                    )
                ], span=3),
                fac.AntdCol([
                    fac.AntdRangePicker(
                        id='operation-time-range',
                        placeholder=['开始时间', '结束时间'],
                        showTime=True,
                        format='YYYY-MM-DD HH:mm:ss'
                    )
                ], span=7)
            ], gutter=16, style={'marginBottom': '16px'}),
            
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '搜索',
                            id='search-operations-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-search')
                        ),
                        fac.AntdButton(
                            '重置',
                            id='reset-operation-search-btn',
                            icon=fac.AntdIcon(icon='antd-redo')
                        )
                    ])
                ], span=6),
                fac.AntdCol([
                    fac.AntdText('快速选择: ', style={'marginRight': '8px'}),
                    fac.AntdSpace([
                        fac.AntdButton('今天', id='quick-today-btn', size='small'),
                        fac.AntdButton('昨天', id='quick-yesterday-btn', size='small'),
                        fac.AntdButton('最近7天', id='quick-week-btn', size='small'),
                        fac.AntdButton('最近30天', id='quick-month-btn', size='small')
                    ])
                ], span=18)
            ])
        ], style={'marginBottom': '24px'}),
        
        # 操作记录列表
        fac.AntdCard([
            fuc.FefferyLoading(
                id='operations-table-loading',
                children=[
                    fac.AntdTable(
                        id='operations-table',
                        columns=[
                            {
                                'title': '操作时间',
                                'dataIndex': 'operationTime',
                                'key': 'operationTime',
                                'width': 180,
                                'fixed': 'left'
                            },
                            {
                                'title': '服务名称',
                                'dataIndex': 'serviceName',
                                'key': 'serviceName',
                                'width': 120
                            },
                            {
                                'title': '操作类型',
                                'dataIndex': 'operationType',
                                'key': 'operationType',
                                'width': 100,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '操作状态',
                                'dataIndex': 'operationStatus',
                                'key': 'operationStatus',
                                'width': 100,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '操作人',
                                'dataIndex': 'operator',
                                'key': 'operator',
                                'width': 100
                            },
                            {
                                'title': '操作IP',
                                'dataIndex': 'operatorIp',
                                'key': 'operatorIp',
                                'width': 120
                            },
                            {
                                'title': '执行时间',
                                'dataIndex': 'executionTime',
                                'key': 'executionTime',
                                'width': 100
                            },
                            {
                                'title': '完成时间',
                                'dataIndex': 'completedTime',
                                'key': 'completedTime',
                                'width': 180
                            },
                            {
                                'title': '操作结果',
                                'dataIndex': 'operationResult',
                                'key': 'operationResult',
                                'ellipsis': True
                            },
                            {
                                'title': '操作',
                                'dataIndex': 'operation',
                                'key': 'operation',
                                'width': 100,
                                'fixed': 'right',
                                'renderOptions': {'renderType': 'button'}
                            }
                        ],
                        data=[],
                        pagination={
                            'pageSize': 20,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True,
                            'showTotal': True
                        },
                        bordered=True,
                        size='small',
                        scroll={'x': 1400}
                    )
                ]
            )
        ], title='操作记录'),
        
        # 操作详情模态框
        fac.AntdModal(
            id='operation-detail-modal',
            title='操作详情',
            visible=False,
            width=800,
            children=[
                html.Div(id='operation-detail-content')
            ],
            footer=fac.AntdButton('关闭', id='close-operation-detail-btn', type='primary')
        ),
        
        # 隐藏的数据存储组件
        dcc.Store(id='operations-data-store'),
        dcc.Store(id='current-operation-store'),
        
        # 消息提示容器
        html.Div(id='operations-message-container')
    ])


@callback(
    Output('operation-time-range', 'value'),
    [Input('quick-today-btn', 'nClicks'),
     Input('quick-yesterday-btn', 'nClicks'),
     Input('quick-week-btn', 'nClicks'),
     Input('quick-month-btn', 'nClicks')],
    prevent_initial_call=True
)
def set_quick_operation_time_range(today_clicks, yesterday_clicks, week_clicks, month_clicks):
    """设置快速时间范围"""
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    now = datetime.now()
    
    if trigger_id == 'quick-today-btn':
        start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = now
    elif trigger_id == 'quick-yesterday-btn':
        yesterday = now - timedelta(days=1)
        start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif trigger_id == 'quick-week-btn':
        start_time = now - timedelta(days=7)
        end_time = now
    elif trigger_id == 'quick-month-btn':
        start_time = now - timedelta(days=30)
        end_time = now
    else:
        return dash.no_update
    
    return [start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')]


@callback(
    [Output('operations-data-store', 'data'),
     Output('operations-table-loading', 'loading')],
    [Input('refresh-operations-btn', 'nClicks'),
     Input('search-operations-btn', 'nClicks'),
     Input('operations-table', 'pagination')],
    [State('operation-service-filter', 'value'),
     State('operation-type-filter', 'value'),
     State('operation-status-filter', 'value'),
     State('operation-operator-search', 'value'),
     State('operation-time-range', 'value')],
    prevent_initial_call=False
)
def load_operations_data(refresh_clicks, search_clicks, pagination,
                        service_name, operation_type, operation_status, operator, time_range):
    """加载操作记录数据"""
    try:
        # 构建查询参数
        params = {}
        if service_name:
            params['service_name'] = service_name
        if operation_type:
            params['operation_type'] = operation_type
        if operation_status:
            params['operation_status'] = operation_status
        if operator:
            params['operator'] = operator
        if time_range and len(time_range) == 2:
            params['start_time'] = time_range[0]
            params['end_time'] = time_range[1]
        
        # 分页参数
        if pagination:
            params['page_num'] = pagination.get('current', 1)
            params['page_size'] = pagination.get('pageSize', 20)
        else:
            params['page_num'] = 1
            params['page_size'] = 20
        
        # 调用API获取操作记录数据
        response = requests.get(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/operations",
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}), False
            else:
                return {}, False
        else:
            return {}, False
            
    except Exception as e:
        print(f"加载操作记录数据失败: {str(e)}")
        return {}, False


@callback(
    [Output('operations-table', 'data'),
     Output('total-operations-stat', 'value'),
     Output('success-operations-stat', 'value'),
     Output('failed-operations-stat', 'value'),
     Output('today-operations-stat', 'value')],
    [Input('operations-data-store', 'data')],
    prevent_initial_call=True
)
def update_operations_display(operations_data):
    """更新操作记录显示"""
    if not operations_data or 'operations' not in operations_data:
        return [], 0, 0, 0, 0
    
    operations = operations_data['operations']
    total_count = operations_data.get('total', 0)
    
    # 统计操作数据
    success_count = sum(1 for op in operations if op.get('operationStatus') == 'success')
    failed_count = sum(1 for op in operations if op.get('operationStatus') == 'failed')
    
    # 统计今日操作
    today = datetime.now().date()
    today_count = 0
    for op in operations:
        op_time = op.get('operationTime', '')
        if op_time:
            try:
                op_date = datetime.fromisoformat(op_time.replace('Z', '+00:00')).date()
                if op_date == today:
                    today_count += 1
            except:
                pass
    
    # 构建表格数据
    table_data = []
    for op in operations:
        # 操作类型标签
        op_type = op.get('operationType', '')
        type_tag = {
            'start': {'tag': '启动', 'color': 'green'},
            'stop': {'tag': '停止', 'color': 'orange'},
            'restart': {'tag': '重启', 'color': 'blue'},
            'config': {'tag': '配置', 'color': 'purple'},
            'deploy': {'tag': '部署', 'color': 'cyan'}
        }.get(op_type, {'tag': op_type, 'color': 'default'})
        
        # 操作状态标签
        status = op.get('operationStatus', '')
        status_tag = {
            'success': {'tag': '成功', 'color': 'green'},
            'failed': {'tag': '失败', 'color': 'red'},
            'running': {'tag': '运行中', 'color': 'blue'},
            'pending': {'tag': '待处理', 'color': 'orange'}
        }.get(status, {'tag': status, 'color': 'default'})
        
        # 执行时间格式化
        execution_time = op.get('executionTimeMs', 0)
        if execution_time:
            if execution_time < 1000:
                execution_display = f"{execution_time}ms"
            else:
                execution_display = f"{execution_time/1000:.2f}s"
        else:
            execution_display = "-"
        
        # 操作按钮
        operation_buttons = [
            {
                'content': '详情',
                'type': 'link',
                'size': 'small',
                'custom': f"detail-{op.get('operationId', '')}"
            }
        ]
        
        table_data.append({
            'key': op.get('operationId', ''),
            'operationTime': op.get('operationTime', ''),
            'serviceName': op.get('serviceName', ''),
            'operationType': type_tag,
            'operationStatus': status_tag,
            'operator': op.get('operator', ''),
            'operatorIp': op.get('operatorIp', ''),
            'executionTime': execution_display,
            'completedTime': op.get('completedTime', ''),
            'operationResult': op.get('operationResult', ''),
            'operation': operation_buttons
        })
    
    return table_data, total_count, success_count, failed_count, today_count


@callback(
    [Output('operation-service-filter', 'value'),
     Output('operation-type-filter', 'value'),
     Output('operation-status-filter', 'value'),
     Output('operation-operator-search', 'value'),
     Output('operation-time-range', 'value')],
    [Input('reset-operation-search-btn', 'nClicks')],
    prevent_initial_call=True
)
def reset_operation_search_form(n_clicks):
    """重置操作记录搜索表单"""
    if n_clicks:
        return '', '', '', '', None
    return dash.no_update
