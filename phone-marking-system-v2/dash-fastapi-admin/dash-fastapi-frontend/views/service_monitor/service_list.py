"""
服务列表管理页面
显示和管理所有核心服务的详细信息和状态
"""
import dash
from dash import html, dcc, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime
import requests
import json

from config.global_config import ApiBaseUrlConfig
from utils.common_util import validate_data_not_empty


def render_service_list():
    """渲染服务列表页面"""
    return html.Div([
        # 页面标题和操作按钮
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdIcon(icon='antd-cluster', style={'fontSize': '24px', 'color': '#1890ff'}),
                    fac.AntdTitle('服务管理', level=2, style={'margin': 0})
                ])
            ], span=12),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '初始化服务',
                        id='init-services-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-plus')
                    ),
                    fac.AntdButton(
                        '刷新列表',
                        id='refresh-services-btn',
                        type='default',
                        icon=fac.AntdIcon(icon='antd-reload')
                    )
                ], style={'float': 'right'})
            ], span=12)
        ], style={'marginBottom': '24px'}),
        
        # 搜索和筛选区域
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdInput(
                        id='service-name-search',
                        placeholder='请输入服务名称',
                        prefix=fac.AntdIcon(icon='antd-search'),
                        allowClear=True
                    )
                ], span=6),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='service-type-filter',
                        placeholder='选择服务类型',
                        options=[
                            {'label': '全部', 'value': ''},
                            {'label': '系统服务', 'value': 'system'},
                            {'label': '应用服务', 'value': 'application'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=4),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='service-status-filter',
                        placeholder='选择服务状态',
                        options=[
                            {'label': '全部', 'value': ''},
                            {'label': '运行中', 'value': 'running'},
                            {'label': '已停止', 'value': 'stopped'},
                            {'label': '异常', 'value': 'error'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=4),
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '搜索',
                            id='search-services-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-search')
                        ),
                        fac.AntdButton(
                            '重置',
                            id='reset-search-btn',
                            icon=fac.AntdIcon(icon='antd-redo')
                        )
                    ])
                ], span=10)
            ], gutter=16)
        ], style={'marginBottom': '24px'}),
        
        # 服务列表表格
        fac.AntdCard([
            fuc.FefferyLoading(
                id='services-table-loading',
                children=[
                    fac.AntdTable(
                        id='services-table',
                        columns=[
                            {
                                'title': '服务名称',
                                'dataIndex': 'serviceName',
                                'key': 'serviceName',
                                'width': 120,
                                'fixed': 'left'
                            },
                            {
                                'title': '显示名称',
                                'dataIndex': 'displayName',
                                'key': 'displayName',
                                'width': 150
                            },
                            {
                                'title': '服务类型',
                                'dataIndex': 'serviceType',
                                'key': 'serviceType',
                                'width': 100
                            },
                            {
                                'title': '运行状态',
                                'dataIndex': 'status',
                                'key': 'status',
                                'width': 100,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '健康状态',
                                'dataIndex': 'healthStatus',
                                'key': 'healthStatus',
                                'width': 100,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '端口',
                                'dataIndex': 'port',
                                'key': 'port',
                                'width': 80
                            },
                            {
                                'title': 'CPU使用率',
                                'dataIndex': 'cpuUsage',
                                'key': 'cpuUsage',
                                'width': 100
                            },
                            {
                                'title': '内存使用率',
                                'dataIndex': 'memoryUsage',
                                'key': 'memoryUsage',
                                'width': 100
                            },
                            {
                                'title': '响应时间',
                                'dataIndex': 'responseTime',
                                'key': 'responseTime',
                                'width': 100
                            },
                            {
                                'title': '是否关键',
                                'dataIndex': 'isCritical',
                                'key': 'isCritical',
                                'width': 100,
                                'renderOptions': {'renderType': 'switch'}
                            },
                            {
                                'title': '最后检查时间',
                                'dataIndex': 'lastCheckTime',
                                'key': 'lastCheckTime',
                                'width': 150
                            },
                            {
                                'title': '操作',
                                'dataIndex': 'operation',
                                'key': 'operation',
                                'width': 200,
                                'fixed': 'right',
                                'renderOptions': {'renderType': 'button'}
                            }
                        ],
                        data=[],
                        pagination={
                            'pageSize': 10,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True,
                            'showTotal': True
                        },
                        bordered=True,
                        size='small',
                        scroll={'x': 1400}
                    )
                ]
            )
        ], title='服务列表'),
        
        # 服务操作确认对话框
        fac.AntdModal(
            id='service-operation-modal',
            title='确认操作',
            visible=False,
            children=[
                html.Div(id='service-operation-modal-content')
            ],
            okText='确认',
            cancelText='取消'
        ),
        
        # 服务详情抽屉
        fac.AntdDrawer(
            id='service-detail-drawer',
            title='服务详情',
            visible=False,
            width=600,
            children=[
                html.Div(id='service-detail-content')
            ]
        ),
        
        # 隐藏的数据存储组件
        dcc.Store(id='services-data-store'),
        dcc.Store(id='current-service-store'),
        dcc.Store(id='search-params-store', data={}),
        
        # 消息提示容器
        html.Div(id='services-message-container')
    ])


@callback(
    [Output('services-data-store', 'data'),
     Output('services-table-loading', 'loading')],
    [Input('refresh-services-btn', 'nClicks'),
     Input('search-services-btn', 'nClicks'),
     Input('services-table', 'pagination')],
    [State('service-name-search', 'value'),
     State('service-type-filter', 'value'),
     State('service-status-filter', 'value')],
    prevent_initial_call=False
)
def load_services_data(refresh_clicks, search_clicks, pagination, 
                      service_name, service_type, service_status):
    """加载服务数据"""
    try:
        # 构建查询参数
        params = {}
        if service_name:
            params['service_name'] = service_name
        if service_type:
            params['service_type'] = service_type
        if service_status:
            params['status'] = service_status
        
        # 分页参数
        if pagination:
            params['page_num'] = pagination.get('current', 1)
            params['page_size'] = pagination.get('pageSize', 10)
        else:
            params['page_num'] = 1
            params['page_size'] = 10
        
        # 调用API获取服务列表
        response = requests.get(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/services",
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}), False
            else:
                return {}, False
        else:
            return {}, False
            
    except Exception as e:
        print(f"加载服务数据失败: {str(e)}")
        return {}, False


@callback(
    Output('services-table', 'data'),
    [Input('services-data-store', 'data')],
    prevent_initial_call=True
)
def update_services_table(services_data):
    """更新服务表格数据"""
    if not services_data or 'services' not in services_data:
        return []
    
    table_data = []
    for service in services_data['services']:
        # 处理状态标签
        status = service.get('latestStatus', {}).get('status', 'unknown')
        status_tag = {
            'running': {'tag': '运行中', 'color': 'green'},
            'stopped': {'tag': '已停止', 'color': 'orange'},
            'error': {'tag': '异常', 'color': 'red'},
            'unknown': {'tag': '未知', 'color': 'gray'}
        }.get(status, {'tag': '未知', 'color': 'gray'})
        
        health_status = service.get('latestStatus', {}).get('healthStatus', 'unknown')
        health_tag = {
            'healthy': {'tag': '健康', 'color': 'green'},
            'unhealthy': {'tag': '不健康', 'color': 'red'},
            'unknown': {'tag': '未知', 'color': 'gray'}
        }.get(health_status, {'tag': '未知', 'color': 'gray'})
        
        # 格式化性能数据
        cpu_usage = service.get('latestStatus', {}).get('cpuUsage')
        memory_usage = service.get('latestStatus', {}).get('memoryUsage')
        response_time = service.get('latestStatus', {}).get('responseTimeMs')
        
        cpu_display = f"{cpu_usage:.1f}%" if cpu_usage is not None else "-"
        memory_display = f"{memory_usage:.1f}%" if memory_usage is not None else "-"
        response_display = f"{response_time:.1f}ms" if response_time is not None else "-"
        
        # 操作按钮
        operation_buttons = [
            {
                'content': '启动',
                'type': 'primary',
                'size': 'small',
                'custom': f"start-{service['serviceName']}"
            },
            {
                'content': '停止',
                'type': 'default',
                'size': 'small',
                'custom': f"stop-{service['serviceName']}"
            },
            {
                'content': '重启',
                'type': 'default',
                'size': 'small',
                'custom': f"restart-{service['serviceName']}"
            },
            {
                'content': '详情',
                'type': 'link',
                'size': 'small',
                'custom': f"detail-{service['serviceName']}"
            }
        ]
        
        table_data.append({
            'key': service['serviceName'],
            'serviceName': service['serviceName'],
            'displayName': service['displayName'],
            'serviceType': '系统服务' if service['serviceType'] == 'system' else '应用服务',
            'status': status_tag,
            'healthStatus': health_tag,
            'port': service.get('port', '-'),
            'cpuUsage': cpu_display,
            'memoryUsage': memory_display,
            'responseTime': response_display,
            'isCritical': service.get('isCritical', False),
            'lastCheckTime': service.get('latestStatus', {}).get('lastCheckTime', '-'),
            'operation': operation_buttons
        })
    
    return table_data


@callback(
    [Output('service-name-search', 'value'),
     Output('service-type-filter', 'value'),
     Output('service-status-filter', 'value')],
    [Input('reset-search-btn', 'nClicks')],
    prevent_initial_call=True
)
def reset_search_form(n_clicks):
    """重置搜索表单"""
    if n_clicks:
        return '', '', ''
    return dash.no_update


@callback(
    Output('services-message-container', 'children'),
    [Input('init-services-btn', 'nClicks')],
    prevent_initial_call=True
)
def init_core_services(n_clicks):
    """初始化核心服务"""
    if not n_clicks:
        return dash.no_update
    
    try:
        # 调用初始化API
        response = requests.post(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/init",
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return fac.AntdMessage(
                    content=f'核心服务初始化成功: {result.get("message", "")}',
                    type='success'
                )
            else:
                return fac.AntdMessage(
                    content=f'初始化失败: {result.get("message", "未知错误")}',
                    type='error'
                )
        else:
            return fac.AntdMessage(
                content='初始化请求失败',
                type='error'
            )
            
    except Exception as e:
        return fac.AntdMessage(
            content=f'初始化失败: {str(e)}',
            type='error'
        )


@callback(
    [Output('service-operation-modal', 'visible'),
     Output('service-operation-modal-content', 'children'),
     Output('current-service-store', 'data')],
    [Input('services-table', 'nClicksButton')],
    [State('services-table', 'clickedCustom'),
     State('services-table', 'recentlyButtonClickedRow')],
    prevent_initial_call=True
)
def handle_service_operation(n_clicks, clicked_custom, clicked_row):
    """处理服务操作按钮点击"""
    if not n_clicks or not clicked_custom or not clicked_row:
        return False, dash.no_update, dash.no_update
    
    action, service_name = clicked_custom.split('-', 1)
    
    if action in ['start', 'stop', 'restart']:
        action_text = {'start': '启动', 'stop': '停止', 'restart': '重启'}[action]
        
        modal_content = [
            fac.AntdText(f'确认要{action_text}服务 '),
            fac.AntdText(service_name, strong=True),
            fac.AntdText(' 吗？'),
            html.Br(),
            html.Br(),
            fac.AntdAlert(
                message=f'此操作将{action_text}指定的服务，请确认后继续。',
                type='warning',
                showIcon=True
            )
        ]
        
        return True, modal_content, {'action': action, 'service_name': service_name}
    
    elif action == 'detail':
        # 显示服务详情抽屉
        return False, dash.no_update, {'action': 'detail', 'service_name': service_name}
    
    return False, dash.no_update, dash.no_update
