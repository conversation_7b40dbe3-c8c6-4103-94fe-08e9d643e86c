"""
服务日志查看页面
提供服务日志的查询、筛选和查看功能
"""
import dash
from dash import html, dcc, Input, Output, State, callback, ctx
import feffery_antd_components as fac
import feffery_utils_components as fuc
from datetime import datetime, timedelta
import requests
import json

from config.global_config import ApiBaseUrlConfig
from utils.common_util import validate_data_not_empty


def render_service_logs():
    """渲染服务日志页面"""
    return html.Div([
        # 页面标题
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdIcon(icon='antd-file-text', style={'fontSize': '24px', 'color': '#1890ff'}),
                    fac.AntdTitle('服务日志', level=2, style={'margin': 0})
                ])
            ], span=12),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdButton(
                        '刷新日志',
                        id='refresh-logs-btn',
                        type='primary',
                        icon=fac.AntdIcon(icon='antd-reload')
                    ),
                    fac.AntdButton(
                        '导出日志',
                        id='export-logs-btn',
                        type='default',
                        icon=fac.AntdIcon(icon='antd-download')
                    )
                ], style={'float': 'right'})
            ], span=12)
        ], style={'marginBottom': '24px'}),
        
        # 日志筛选区域
        fac.AntdCard([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSelect(
                        id='log-service-filter',
                        placeholder='选择服务',
                        options=[
                            {'label': '全部服务', 'value': ''},
                            {'label': 'MySQL数据库', 'value': 'mysql'},
                            {'label': 'Redis缓存', 'value': 'redis'},
                            {'label': '后端API服务', 'value': 'backend'},
                            {'label': '前端Web服务', 'value': 'frontend'},
                            {'label': '归属地服务', 'value': 'location'},
                            {'label': 'NLP处理服务', 'value': 'nlp'},
                            {'label': '批处理服务', 'value': 'batch'},
                            {'label': 'API网关', 'value': 'gateway'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=4),
                fac.AntdCol([
                    fac.AntdSelect(
                        id='log-level-filter',
                        placeholder='选择日志级别',
                        options=[
                            {'label': '全部级别', 'value': ''},
                            {'label': 'DEBUG', 'value': 'DEBUG'},
                            {'label': 'INFO', 'value': 'INFO'},
                            {'label': 'WARNING', 'value': 'WARNING'},
                            {'label': 'ERROR', 'value': 'ERROR'},
                            {'label': 'CRITICAL', 'value': 'CRITICAL'}
                        ],
                        value='',
                        allowClear=True
                    )
                ], span=3),
                fac.AntdCol([
                    fac.AntdRangePicker(
                        id='log-time-range',
                        placeholder=['开始时间', '结束时间'],
                        showTime=True,
                        format='YYYY-MM-DD HH:mm:ss'
                    )
                ], span=6),
                fac.AntdCol([
                    fac.AntdInput(
                        id='log-keyword-search',
                        placeholder='搜索关键词',
                        prefix=fac.AntdIcon(icon='antd-search'),
                        allowClear=True
                    )
                ], span=5),
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdButton(
                            '搜索',
                            id='search-logs-btn',
                            type='primary',
                            icon=fac.AntdIcon(icon='antd-search')
                        ),
                        fac.AntdButton(
                            '重置',
                            id='reset-log-search-btn',
                            icon=fac.AntdIcon(icon='antd-redo')
                        )
                    ])
                ], span=6)
            ], gutter=16, style={'marginBottom': '16px'}),
            
            # 快速时间选择
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdText('快速选择: ', style={'marginRight': '8px'}),
                    fac.AntdSpace([
                        fac.AntdButton('最近1小时', id='quick-1h-btn', size='small'),
                        fac.AntdButton('最近6小时', id='quick-6h-btn', size='small'),
                        fac.AntdButton('最近24小时', id='quick-24h-btn', size='small'),
                        fac.AntdButton('最近7天', id='quick-7d-btn', size='small')
                    ])
                ])
            ])
        ], style={'marginBottom': '24px'}),
        
        # 日志统计信息
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='总日志数',
                        value=0,
                        id='total-logs-stat',
                        valueStyle={'color': '#1890ff'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='错误日志',
                        value=0,
                        id='error-logs-stat',
                        valueStyle={'color': '#ff4d4f'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='警告日志',
                        value=0,
                        id='warning-logs-stat',
                        valueStyle={'color': '#faad14'}
                    )
                ])
            ], span=6),
            fac.AntdCol([
                fac.AntdCard([
                    fac.AntdStatistic(
                        title='信息日志',
                        value=0,
                        id='info-logs-stat',
                        valueStyle={'color': '#52c41a'}
                    )
                ])
            ], span=6)
        ], gutter=16, style={'marginBottom': '24px'}),
        
        # 日志列表
        fac.AntdCard([
            fuc.FefferyLoading(
                id='logs-table-loading',
                children=[
                    fac.AntdTable(
                        id='logs-table',
                        columns=[
                            {
                                'title': '时间',
                                'dataIndex': 'logTime',
                                'key': 'logTime',
                                'width': 180,
                                'fixed': 'left'
                            },
                            {
                                'title': '服务',
                                'dataIndex': 'serviceName',
                                'key': 'serviceName',
                                'width': 100
                            },
                            {
                                'title': '级别',
                                'dataIndex': 'logLevel',
                                'key': 'logLevel',
                                'width': 80,
                                'renderOptions': {'renderType': 'tags'}
                            },
                            {
                                'title': '来源',
                                'dataIndex': 'logSource',
                                'key': 'logSource',
                                'width': 120
                            },
                            {
                                'title': '消息',
                                'dataIndex': 'logMessage',
                                'key': 'logMessage',
                                'ellipsis': True
                            },
                            {
                                'title': '用户',
                                'dataIndex': 'userId',
                                'key': 'userId',
                                'width': 100
                            },
                            {
                                'title': 'IP地址',
                                'dataIndex': 'ipAddress',
                                'key': 'ipAddress',
                                'width': 120
                            },
                            {
                                'title': '操作',
                                'dataIndex': 'operation',
                                'key': 'operation',
                                'width': 100,
                                'fixed': 'right',
                                'renderOptions': {'renderType': 'button'}
                            }
                        ],
                        data=[],
                        pagination={
                            'pageSize': 20,
                            'current': 1,
                            'showSizeChanger': True,
                            'showQuickJumper': True,
                            'showTotal': True
                        },
                        bordered=True,
                        size='small',
                        scroll={'x': 1200}
                    )
                ]
            )
        ], title='日志记录'),
        
        # 日志详情模态框
        fac.AntdModal(
            id='log-detail-modal',
            title='日志详情',
            visible=False,
            width=800,
            children=[
                html.Div(id='log-detail-content')
            ],
            footer=fac.AntdButton('关闭', id='close-log-detail-btn', type='primary')
        ),
        
        # 隐藏的数据存储组件
        dcc.Store(id='logs-data-store'),
        dcc.Store(id='log-search-params-store', data={}),
        dcc.Store(id='current-log-store'),
        
        # 消息提示容器
        html.Div(id='logs-message-container')
    ])


@callback(
    Output('log-time-range', 'value'),
    [Input('quick-1h-btn', 'nClicks'),
     Input('quick-6h-btn', 'nClicks'),
     Input('quick-24h-btn', 'nClicks'),
     Input('quick-7d-btn', 'nClicks')],
    prevent_initial_call=True
)
def set_quick_time_range(h1_clicks, h6_clicks, h24_clicks, d7_clicks):
    """设置快速时间范围"""
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    now = datetime.now()
    
    if trigger_id == 'quick-1h-btn':
        start_time = now - timedelta(hours=1)
    elif trigger_id == 'quick-6h-btn':
        start_time = now - timedelta(hours=6)
    elif trigger_id == 'quick-24h-btn':
        start_time = now - timedelta(hours=24)
    elif trigger_id == 'quick-7d-btn':
        start_time = now - timedelta(days=7)
    else:
        return dash.no_update
    
    return [start_time.strftime('%Y-%m-%d %H:%M:%S'), now.strftime('%Y-%m-%d %H:%M:%S')]


@callback(
    [Output('logs-data-store', 'data'),
     Output('logs-table-loading', 'loading')],
    [Input('refresh-logs-btn', 'nClicks'),
     Input('search-logs-btn', 'nClicks'),
     Input('logs-table', 'pagination')],
    [State('log-service-filter', 'value'),
     State('log-level-filter', 'value'),
     State('log-time-range', 'value'),
     State('log-keyword-search', 'value')],
    prevent_initial_call=False
)
def load_logs_data(refresh_clicks, search_clicks, pagination,
                  service_name, log_level, time_range, keyword):
    """加载日志数据"""
    try:
        # 构建查询参数
        params = {}
        if service_name:
            params['service_name'] = service_name
        if log_level:
            params['log_level'] = log_level
        if keyword:
            params['keyword'] = keyword
        if time_range and len(time_range) == 2:
            params['start_time'] = time_range[0]
            params['end_time'] = time_range[1]
        
        # 分页参数
        if pagination:
            params['page_num'] = pagination.get('current', 1)
            params['page_size'] = pagination.get('pageSize', 20)
        else:
            params['page_num'] = 1
            params['page_size'] = 20
        
        # 调用API获取日志数据
        response = requests.get(
            f"{ApiBaseUrlConfig.BaseUrl}/monitor/logs",
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                return result.get('data', {}), False
            else:
                return {}, False
        else:
            return {}, False
            
    except Exception as e:
        print(f"加载日志数据失败: {str(e)}")
        return {}, False


@callback(
    [Output('logs-table', 'data'),
     Output('total-logs-stat', 'value'),
     Output('error-logs-stat', 'value'),
     Output('warning-logs-stat', 'value'),
     Output('info-logs-stat', 'value')],
    [Input('logs-data-store', 'data')],
    prevent_initial_call=True
)
def update_logs_display(logs_data):
    """更新日志显示"""
    if not logs_data or 'logs' not in logs_data:
        return [], 0, 0, 0, 0
    
    logs = logs_data['logs']
    total_count = logs_data.get('total', 0)
    
    # 统计各级别日志数量
    error_count = sum(1 for log in logs if log.get('logLevel') == 'ERROR')
    warning_count = sum(1 for log in logs if log.get('logLevel') == 'WARNING')
    info_count = sum(1 for log in logs if log.get('logLevel') == 'INFO')
    
    # 构建表格数据
    table_data = []
    for log in logs:
        # 日志级别标签
        level = log.get('logLevel', 'INFO')
        level_tag = {
            'DEBUG': {'tag': 'DEBUG', 'color': 'blue'},
            'INFO': {'tag': 'INFO', 'color': 'green'},
            'WARNING': {'tag': 'WARNING', 'color': 'orange'},
            'ERROR': {'tag': 'ERROR', 'color': 'red'},
            'CRITICAL': {'tag': 'CRITICAL', 'color': 'purple'}
        }.get(level, {'tag': level, 'color': 'default'})
        
        # 操作按钮
        operation_buttons = [
            {
                'content': '详情',
                'type': 'link',
                'size': 'small',
                'custom': f"detail-{log.get('logId', '')}"
            }
        ]
        
        table_data.append({
            'key': log.get('logId', ''),
            'logTime': log.get('logTime', ''),
            'serviceName': log.get('serviceName', ''),
            'logLevel': level_tag,
            'logSource': log.get('logSource', ''),
            'logMessage': log.get('logMessage', ''),
            'userId': log.get('userId', ''),
            'ipAddress': log.get('ipAddress', ''),
            'operation': operation_buttons
        })
    
    return table_data, total_count, error_count, warning_count, info_count


@callback(
    [Output('log-service-filter', 'value'),
     Output('log-level-filter', 'value'),
     Output('log-time-range', 'value'),
     Output('log-keyword-search', 'value')],
    [Input('reset-log-search-btn', 'nClicks')],
    prevent_initial_call=True
)
def reset_log_search_form(n_clicks):
    """重置日志搜索表单"""
    if n_clicks:
        return '', '', None, ''
    return dash.no_update


@callback(
    [Output('log-detail-modal', 'visible'),
     Output('log-detail-content', 'children'),
     Output('current-log-store', 'data')],
    [Input('logs-table', 'nClicksButton'),
     Input('close-log-detail-btn', 'nClicks')],
    [State('logs-table', 'clickedCustom'),
     State('logs-table', 'recentlyButtonClickedRow'),
     State('logs-data-store', 'data')],
    prevent_initial_call=True
)
def handle_log_detail(table_clicks, close_clicks, clicked_custom, clicked_row, logs_data):
    """处理日志详情显示"""
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    if trigger_id == 'close-log-detail-btn':
        return False, dash.no_update, dash.no_update
    
    if trigger_id == 'logs-table' and clicked_custom and clicked_row:
        action, log_id = clicked_custom.split('-', 1)
        
        if action == 'detail' and logs_data and 'logs' in logs_data:
            # 查找对应的日志记录
            log_detail = None
            for log in logs_data['logs']:
                if str(log.get('logId', '')) == log_id:
                    log_detail = log
                    break
            
            if log_detail:
                detail_content = [
                    fac.AntdDescriptions(
                        items=[
                            {'label': '日志ID', 'children': log_detail.get('logId', '')},
                            {'label': '服务名称', 'children': log_detail.get('serviceName', '')},
                            {'label': '日志级别', 'children': log_detail.get('logLevel', '')},
                            {'label': '日志来源', 'children': log_detail.get('logSource', '')},
                            {'label': '用户ID', 'children': log_detail.get('userId', '')},
                            {'label': 'IP地址', 'children': log_detail.get('ipAddress', '')},
                            {'label': '请求ID', 'children': log_detail.get('requestId', '')},
                            {'label': '日志时间', 'children': log_detail.get('logTime', '')}
                        ],
                        column=2,
                        bordered=True
                    ),
                    html.Br(),
                    fac.AntdTitle('日志消息', level=5),
                    fac.AntdInput(
                        value=log_detail.get('logMessage', ''),
                        mode='text-area',
                        autoSize={'minRows': 3, 'maxRows': 6},
                        readOnly=True
                    ),
                    html.Br(),
                    html.Br(),
                    fac.AntdTitle('异常信息', level=5) if log_detail.get('exceptionInfo') else None,
                    fac.AntdInput(
                        value=log_detail.get('exceptionInfo', ''),
                        mode='text-area',
                        autoSize={'minRows': 5, 'maxRows': 10},
                        readOnly=True
                    ) if log_detail.get('exceptionInfo') else None,
                    html.Br() if log_detail.get('exceptionInfo') else None,
                    html.Br() if log_detail.get('exceptionInfo') else None,
                    fac.AntdTitle('详细信息', level=5) if log_detail.get('logDetails') else None,
                    fac.AntdInput(
                        value=json.dumps(log_detail.get('logDetails', {}), indent=2, ensure_ascii=False),
                        mode='text-area',
                        autoSize={'minRows': 3, 'maxRows': 8},
                        readOnly=True
                    ) if log_detail.get('logDetails') else None
                ]
                
                # 过滤掉None值
                detail_content = [item for item in detail_content if item is not None]
                
                return True, detail_content, log_detail
    
    return False, dash.no_update, dash.no_update


@callback(
    Output('logs-message-container', 'children'),
    [Input('export-logs-btn', 'nClicks')],
    [State('log-search-params-store', 'data')],
    prevent_initial_call=True
)
def export_logs(n_clicks, search_params):
    """导出日志"""
    if not n_clicks:
        return dash.no_update
    
    # 这里可以实现日志导出功能
    return fac.AntdMessage(
        content='日志导出功能开发中...',
        type='info'
    )
