# 系统运维完整指南

## 📋 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [服务管理](#服务管理)
4. [监控与诊断](#监控与诊断)
5. [自动修复](#自动修复)
6. [备份与恢复](#备份与恢复)
7. [性能优化](#性能优化)
8. [故障排除](#故障排除)
9. [最佳实践](#最佳实践)

## 🎯 系统概述

电话标记系统运维管理平台是一个智能化的服务管理工具，提供：

### 核心功能
- **一键启动**：自动启动所有系统服务
- **智能监控**：实时监控服务状态和系统资源
- **自动修复**：智能检测并修复系统问题
- **备份管理**：自动化数据备份和恢复
- **性能优化**：系统性能监控和优化建议

### 系统架构
```
电话标记系统
├── 核心服务
│   ├── MySQL数据库
│   ├── Redis缓存
│   ├── Backend后端服务
│   └── Frontend前端服务
├── 微服务
│   ├── Location位置服务
│   ├── NLP自然语言处理
│   ├── Batch批处理服务
│   └── Gateway网关服务
└── 运维管理
    ├── 服务管理器
    ├── 健康检查器
    ├── 自动修复器
    └── 备份管理器
```

## 🚀 快速开始

### 环境要求
- **操作系统**：macOS / Linux / Windows
- **Python版本**：3.8+
- **数据库**：MySQL 8.0+
- **缓存**：Redis 6.0+
- **内存**：建议4GB+
- **磁盘空间**：建议10GB+

### 一键启动
```bash
# 进入运维管理目录
cd phone-marking-system-v2/scripts/ops

# 启动所有服务
bash main.sh start

# 查看服务状态
bash main.sh status

# 停止所有服务
bash main.sh stop
```

### 初次部署
```bash
# 1. 检查系统环境
bash main.sh check

# 2. 安装依赖
bash main.sh install

# 3. 初始化数据库
bash main.sh init

# 4. 启动系统
bash main.sh start

# 5. 验证部署
bash main.sh test
```

## 🔧 服务管理

### 服务列表
| 服务名称 | 端口 | 类型 | 描述 |
|---------|------|------|------|
| mysql | 3306 | 核心 | 主数据库 |
| redis | 6379 | 核心 | 缓存服务 |
| backend | 9099 | 核心 | 后端API服务 |
| frontend | 8088 | 核心 | 前端Web服务 |
| location | 8001 | 微服务 | 位置识别服务 |
| nlp | 8002 | 微服务 | 自然语言处理 |
| batch | 8003 | 微服务 | 批处理服务 |
| gateway | 8000 | 微服务 | API网关 |

### 服务操作命令
```bash
# 启动单个服务
bash main.sh start <服务名>

# 停止单个服务
bash main.sh stop <服务名>

# 重启单个服务
bash main.sh restart <服务名>

# 查看服务日志
bash main.sh logs <服务名>

# 查看服务详细状态
bash main.sh info <服务名>
```

### 服务依赖关系
```
启动顺序：
1. MySQL → Redis (基础服务)
2. Backend → Frontend (核心服务)
3. Location → NLP → Batch → Gateway (微服务)

停止顺序：
1. Gateway → Batch → NLP → Location (微服务)
2. Frontend → Backend (核心服务)
3. Redis → MySQL (基础服务)
```

## 📊 监控与诊断

### 系统监控
```bash
# 查看系统状态概览
bash main.sh status

# 查看详细系统信息
bash main.sh info

# 查看资源使用情况
bash main.sh resources

# 查看性能指标
bash main.sh performance
```

### 健康检查
```bash
# 执行完整健康检查
bash main.sh health

# 检查特定服务
bash main.sh health <服务名>

# 检查数据库连接
bash main.sh check_db

# 检查网络连接
bash main.sh check_network
```

### 日志管理
```bash
# 查看系统日志
bash main.sh logs system

# 查看操作日志
bash main.sh logs operations

# 查看错误日志
bash main.sh logs error

# 清理旧日志
bash main.sh clean_logs
```

## 🔄 自动修复

### 修复功能
- **智能检测**：自动发现系统问题
- **自动修复**：无需人工干预的问题修复
- **修复建议**：提供详细的手动修复指导
- **修复记录**：完整的修复操作日志

### 修复命令
```bash
# 执行完整自动修复
bash main.sh repair

# 修复特定类型问题
bash main.sh repair_service <服务名>
bash main.sh repair_database
bash main.sh repair_network
bash main.sh repair_resources

# 查看修复历史
bash main.sh repair_history
```

### 修复场景
1. **服务异常停止**：自动重启服务
2. **依赖包缺失**：自动安装依赖
3. **端口被占用**：自动释放端口
4. **配置文件损坏**：自动修复配置
5. **数据库连接失败**：自动重连数据库
6. **内存使用过高**：自动清理内存

## 💾 备份与恢复

### 自动备份
```bash
# 执行完整备份
bash main.sh backup

# 备份数据库
bash main.sh backup_db

# 备份配置文件
bash main.sh backup_config

# 备份日志文件
bash main.sh backup_logs

# 查看备份列表
bash main.sh list_backups
```

### 数据恢复
```bash
# 恢复最新备份
bash main.sh restore

# 恢复指定备份
bash main.sh restore <备份文件名>

# 恢复数据库
bash main.sh restore_db <备份文件>

# 恢复配置文件
bash main.sh restore_config <备份文件>
```

### 备份策略
- **每日自动备份**：凌晨2点执行
- **增量备份**：每4小时执行
- **保留策略**：保留30天内的备份
- **异地备份**：支持云存储备份

## ⚡ 性能优化

### 性能监控
```bash
# 查看性能报告
bash main.sh performance

# 查看资源使用趋势
bash main.sh trends

# 查看慢查询日志
bash main.sh slow_queries

# 生成性能分析报告
bash main.sh analyze
```

### 优化建议
1. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池调优

2. **缓存优化**
   - Redis内存优化
   - 缓存策略调整
   - 过期策略优化

3. **服务优化**
   - 并发处理优化
   - 内存使用优化
   - 响应时间优化

### 自动优化
```bash
# 执行自动优化
bash main.sh optimize

# 优化数据库
bash main.sh optimize_db

# 优化缓存
bash main.sh optimize_cache

# 优化服务配置
bash main.sh optimize_services
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i:<端口号>

# 检查依赖安装
python3 -m pip list

# 查看详细错误日志
tail -f logs/<服务名>.log

# 手动启动调试
cd <服务目录>
python3 <启动文件>
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
brew services list | grep mysql  # macOS
systemctl status mysql          # Linux

# 测试数据库连接
mysql -u root -p -h localhost

# 检查数据库配置
cat dash-fastapi-admin/dash-fastapi-backend/env.py
```

#### 3. 内存使用过高
```bash
# 查看内存使用情况
bash main.sh resources

# 查看进程内存使用
ps aux --sort=-%mem | head

# 重启内存占用高的服务
bash main.sh restart <服务名>
```

#### 4. 网络连接问题
```bash
# 检查端口监听状态
netstat -tlnp | grep <端口号>

# 测试服务连接
curl -I http://localhost:<端口号>

# 检查防火墙设置
sudo ufw status  # Linux
```

### 调试模式
```bash
# 启用调试模式
export DEBUG=1
bash main.sh start

# 查看调试日志
tail -f logs/debug.log

# 详细健康检查
bash main.sh health --verbose
```

## 📋 最佳实践

### 日常运维
1. **每日检查**
   ```bash
   # 每天执行健康检查
   bash main.sh health
   
   # 查看系统状态
   bash main.sh status
   
   # 检查日志异常
   bash main.sh logs error
   ```

2. **定期维护**
   ```bash
   # 每周执行系统优化
   bash main.sh optimize
   
   # 每月清理旧日志
   bash main.sh clean_logs
   
   # 每月执行完整备份
   bash main.sh backup
   ```

3. **监控告警**
   - 设置服务状态监控
   - 配置资源使用告警
   - 建立故障通知机制

### 安全建议
1. **访问控制**
   - 限制管理端口访问
   - 使用强密码策略
   - 定期更新系统补丁

2. **数据安全**
   - 定期备份重要数据
   - 加密敏感配置信息
   - 监控异常访问行为

3. **日志审计**
   - 保留完整操作日志
   - 定期审计系统访问
   - 监控异常操作行为

### 性能调优
1. **资源配置**
   - 根据负载调整服务配置
   - 优化数据库连接池
   - 调整缓存大小

2. **监控指标**
   - CPU使用率 < 80%
   - 内存使用率 < 85%
   - 磁盘使用率 < 90%
   - 响应时间 < 2秒

### 故障预防
1. **预警机制**
   - 设置资源使用阈值
   - 监控服务响应时间
   - 检测异常错误率

2. **容灾准备**
   - 制定故障恢复计划
   - 准备备用服务器
   - 建立数据恢复流程

---

*最后更新：2024年12月*
*版本：v1.0.0*
