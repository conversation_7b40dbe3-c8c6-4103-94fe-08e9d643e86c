# 故障排除手册

## 📋 概述

本手册提供电话标记系统常见故障的诊断和解决方案，帮助运维人员快速定位和解决系统问题。

## 🚨 紧急故障处理

### 系统完全无法访问
```bash
# 1. 快速诊断
bash main.sh status

# 2. 查看系统日志
tail -f logs/system.log

# 3. 执行紧急修复
bash main.sh repair

# 4. 如果修复失败，重启所有服务
bash main.sh stop
bash main.sh start
```

### 数据库连接失败
```bash
# 1. 检查MySQL服务状态
# macOS
brew services list | grep mysql
brew services restart mysql

# Linux
sudo systemctl status mysql
sudo systemctl restart mysql

# Windows
net start mysql

# 2. 测试数据库连接
mysql -u root -p -h localhost

# 3. 检查数据库配置
cat dash-fastapi-admin/dash-fastapi-backend/env.py
```

## 🔧 服务故障排除

### Backend服务故障

#### 症状：Backend服务无法启动
```bash
# 诊断步骤
1. 检查端口占用
   lsof -i:9099
   
2. 查看服务日志
   tail -f logs/backend.log
   
3. 检查Python环境
   python3 --version
   which python3
   
4. 检查依赖安装
   cd dash-fastapi-admin
   python3 -m pip install -r requirements.txt
   
5. 手动启动测试
   cd dash-fastapi-admin/dash-fastapi-backend
   python3 -m uvicorn app:app --host 0.0.0.0 --port 9099
```

#### 症状：Backend服务频繁重启
```bash
# 可能原因和解决方案
1. 内存不足
   # 查看内存使用
   bash main.sh resources
   # 增加系统内存或优化代码
   
2. 数据库连接超时
   # 检查数据库连接池配置
   # 优化数据库查询
   
3. 依赖包冲突
   # 重新安装依赖
   python3 -m pip install -r requirements.txt --force-reinstall
```

### Frontend服务故障

#### 症状：前端页面无法访问
```bash
# 诊断步骤
1. 检查Frontend服务状态
   bash main.sh status frontend
   
2. 检查端口8088是否被占用
   lsof -i:8088
   
3. 查看Frontend日志
   tail -f logs/frontend.log
   
4. 测试服务连接
   curl -I http://localhost:8088
   
5. 检查静态文件
   ls -la dash-fastapi-admin/dash-fastapi-frontend/static/
```

#### 症状：前端页面加载缓慢
```bash
# 优化方案
1. 检查Backend API响应时间
   curl -w "@curl-format.txt" -o /dev/null -s http://localhost:9099/api/health
   
2. 优化静态资源
   # 压缩CSS/JS文件
   # 启用浏览器缓存
   
3. 检查网络连接
   ping localhost
   traceroute localhost
```

### 微服务故障排除

#### Location服务故障
```bash
# 常见问题
1. 地理位置数据库缺失
   # 检查数据文件
   ls -la src/services/location/data/
   
2. API调用超时
   # 调整超时配置
   # 优化查询算法
   
3. 内存使用过高
   # 优化数据加载方式
   # 实现数据分页
```

#### NLP服务故障
```bash
# 常见问题
1. 模型文件缺失
   # 检查模型文件
   ls -la src/services/nlp/models/
   
2. 中文分词错误
   # 检查jieba分词库
   python3 -c "import jieba; print('OK')"
   
3. 处理速度慢
   # 优化模型加载
   # 实现结果缓存
```

## 🗄️ 数据库故障排除

### MySQL连接问题

#### 症状：连接被拒绝
```bash
# 解决步骤
1. 检查MySQL服务状态
   brew services list | grep mysql  # macOS
   sudo systemctl status mysql      # Linux
   
2. 检查MySQL配置
   cat /etc/mysql/mysql.conf.d/mysqld.cnf  # Linux
   cat /usr/local/etc/my.cnf               # macOS
   
3. 检查用户权限
   mysql -u root -p
   SHOW GRANTS FOR 'root'@'localhost';
   
4. 重置root密码（如果需要）
   sudo mysql_secure_installation
```

#### 症状：查询速度慢
```bash
# 优化方案
1. 查看慢查询日志
   mysql -u root -p
   SHOW VARIABLES LIKE 'slow_query_log';
   SHOW VARIABLES LIKE 'long_query_time';
   
2. 分析查询计划
   EXPLAIN SELECT * FROM your_table WHERE condition;
   
3. 优化索引
   SHOW INDEX FROM your_table;
   CREATE INDEX idx_name ON your_table(column_name);
   
4. 优化配置
   # 调整innodb_buffer_pool_size
   # 调整query_cache_size
```

### Redis连接问题

#### 症状：Redis连接失败
```bash
# 解决步骤
1. 检查Redis服务状态
   brew services list | grep redis  # macOS
   sudo systemctl status redis      # Linux
   
2. 测试Redis连接
   redis-cli ping
   
3. 检查Redis配置
   cat /usr/local/etc/redis.conf    # macOS
   cat /etc/redis/redis.conf        # Linux
   
4. 检查内存使用
   redis-cli info memory
```

## 🌐 网络故障排除

### 端口占用问题
```bash
# 查找占用端口的进程
lsof -i:端口号

# 终止占用进程
kill -9 PID

# 查看所有监听端口
netstat -tlnp

# 检查防火墙设置
sudo ufw status  # Linux
```

### API调用失败
```bash
# 测试API连接
curl -v http://localhost:9099/api/health

# 检查API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:9099/api/health

# 查看API日志
tail -f logs/backend.log | grep ERROR
```

## 💾 存储故障排除

### 磁盘空间不足
```bash
# 查看磁盘使用情况
df -h

# 查找大文件
du -sh * | sort -hr

# 清理日志文件
bash main.sh clean_logs

# 清理临时文件
rm -rf /tmp/*
```

### 文件权限问题
```bash
# 检查文件权限
ls -la logs/
ls -la dash-fastapi-admin/

# 修复权限
chmod -R 755 logs/
chmod -R 755 dash-fastapi-admin/

# 修复所有者
chown -R $USER:$USER logs/
chown -R $USER:$USER dash-fastapi-admin/
```

## 🔍 性能故障排除

### CPU使用率过高
```bash
# 查看CPU使用情况
top -o cpu
htop

# 查看进程CPU使用
ps aux --sort=-%cpu | head

# 分析CPU使用模式
sar -u 1 10

# 优化方案
1. 优化代码算法
2. 增加服务实例
3. 使用缓存减少计算
```

### 内存使用过高
```bash
# 查看内存使用情况
free -h
vm_stat  # macOS

# 查看进程内存使用
ps aux --sort=-%mem | head

# 查看内存泄漏
valgrind --tool=memcheck python3 your_script.py

# 优化方案
1. 优化数据结构
2. 实现对象池
3. 定期重启服务
```

## 🔐 安全故障排除

### 异常访问检测
```bash
# 查看访问日志
tail -f logs/access.log

# 分析异常IP
awk '{print $1}' logs/access.log | sort | uniq -c | sort -nr

# 检查失败登录
grep "Failed" logs/auth.log

# 封禁异常IP
sudo ufw deny from 异常IP地址
```

### 权限异常
```bash
# 检查文件权限
find . -type f -perm 777

# 检查用户权限
id $USER
groups $USER

# 修复权限问题
chmod 644 config_files
chmod 755 executable_files
```

## 📊 监控和告警

### 设置监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

# 检查服务状态
if ! bash main.sh status >/dev/null 2>&1; then
    echo "服务异常，发送告警"
    # 发送邮件或短信告警
fi

# 检查资源使用
CPU_USAGE=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
if [ ${CPU_USAGE%.*} -gt 80 ]; then
    echo "CPU使用率过高: ${CPU_USAGE}%"
fi

# 检查磁盘空间
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "磁盘空间不足: ${DISK_USAGE}%"
fi
```

### 自动化故障恢复
```bash
#!/bin/bash
# auto_recovery.sh - 自动故障恢复脚本

# 检查并修复服务
bash main.sh repair

# 如果修复失败，重启服务
if [ $? -ne 0 ]; then
    echo "自动修复失败，执行服务重启"
    bash main.sh restart
fi

# 发送恢复通知
echo "系统已自动恢复" | mail -s "系统恢复通知" <EMAIL>
```

## 📝 故障记录模板

### 故障报告模板
```
故障报告 #YYYY-MM-DD-001

基本信息：
- 发生时间：YYYY-MM-DD HH:MM:SS
- 影响范围：[全系统/部分服务/单个服务]
- 严重程度：[紧急/高/中/低]
- 报告人：[姓名]

故障描述：
- 故障现象：[详细描述]
- 错误信息：[错误日志]
- 影响用户：[用户数量/业务影响]

诊断过程：
1. [诊断步骤1]
2. [诊断步骤2]
3. [诊断步骤3]

解决方案：
- 临时解决方案：[描述]
- 永久解决方案：[描述]
- 预防措施：[描述]

恢复时间：
- 故障发现时间：HH:MM:SS
- 开始处理时间：HH:MM:SS
- 服务恢复时间：HH:MM:SS
- 总恢复时长：XX分钟

经验总结：
- 根本原因：[分析]
- 改进建议：[建议]
- 后续行动：[计划]
```

## 📞 紧急联系方式

### 技术支持
- **紧急热线**：400-xxx-xxxx
- **技术邮箱**：<EMAIL>
- **在线支持**：https://support.example.com

### 升级流程
1. **L1支持**：基础故障排除（0-30分钟）
2. **L2支持**：复杂问题诊断（30分钟-2小时）
3. **L3支持**：架构级问题解决（2小时+）

---

*最后更新：2024年12月*
*版本：v1.0.0*
