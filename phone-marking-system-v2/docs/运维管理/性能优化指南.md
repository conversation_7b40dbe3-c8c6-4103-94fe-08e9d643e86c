# 性能优化指南

## 📋 概述

本指南提供电话标记系统的性能优化策略和最佳实践，帮助提升系统整体性能和用户体验。

## 🎯 性能目标

### 关键性能指标 (KPI)
- **响应时间**：API响应时间 < 200ms
- **吞吐量**：支持1000+ 并发请求
- **可用性**：系统可用性 > 99.9%
- **资源使用**：CPU < 70%，内存 < 80%
- **错误率**：系统错误率 < 0.1%

### 性能基准
```bash
# 查看当前性能指标
bash main.sh performance

# 生成性能报告
bash main.sh analyze

# 性能压力测试
bash main.sh benchmark
```

## 🗄️ 数据库性能优化

### MySQL优化

#### 1. 索引优化
```sql
-- 查看表索引使用情况
SHOW INDEX FROM phone_records;

-- 创建复合索引
CREATE INDEX idx_phone_area_time ON phone_records(phone_number, area_code, created_at);

-- 查看索引使用统计
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'phone_marking_db';

-- 分析查询执行计划
EXPLAIN SELECT * FROM phone_records WHERE phone_number = '13800138000';
```

#### 2. 查询优化
```sql
-- 优化前：全表扫描
SELECT * FROM phone_records WHERE SUBSTRING(phone_number, 1, 3) = '138';

-- 优化后：使用索引
SELECT * FROM phone_records WHERE phone_number LIKE '138%';

-- 使用LIMIT限制结果集
SELECT * FROM phone_records ORDER BY created_at DESC LIMIT 100;

-- 避免SELECT *，只查询需要的字段
SELECT phone_number, mark_type, created_at FROM phone_records WHERE id > 1000;
```

#### 3. 配置优化
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 缓冲池大小（建议为系统内存的70-80%）
innodb_buffer_pool_size = 2G

# 日志文件大小
innodb_log_file_size = 256M

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 连接数配置
max_connections = 200
max_connect_errors = 10000

# 慢查询日志
slow_query_log = 1
long_query_time = 2
slow_query_log_file = /var/log/mysql/slow.log
```

#### 4. 分区表优化
```sql
-- 按时间分区
CREATE TABLE phone_records_partitioned (
    id INT AUTO_INCREMENT,
    phone_number VARCHAR(20),
    mark_type VARCHAR(50),
    created_at DATETIME,
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### Redis优化

#### 1. 内存优化
```bash
# 查看Redis内存使用
redis-cli info memory

# 设置内存使用限制
redis-cli config set maxmemory 1gb
redis-cli config set maxmemory-policy allkeys-lru

# 优化数据结构
# 使用Hash代替String存储对象
redis-cli hset user:1001 name "张三" phone "13800138000" area "北京"
```

#### 2. 持久化优化
```bash
# redis.conf配置
# RDB持久化
save 900 1
save 300 10
save 60 10000

# AOF持久化
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
```

#### 3. 缓存策略优化
```python
# 缓存预热
def warm_up_cache():
    """缓存预热"""
    # 预加载热点数据
    hot_phones = get_hot_phone_numbers()
    for phone in hot_phones:
        cache_phone_info(phone)

# 缓存更新策略
def update_cache_strategy():
    """缓存更新策略"""
    # 写入时更新缓存
    # 设置合理的过期时间
    # 使用版本号避免缓存雪崩
```

## 🚀 应用服务优化

### Backend服务优化

#### 1. 代码优化
```python
# 数据库连接池优化
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)

# 异步处理优化
import asyncio
import aiohttp

async def batch_process_phones(phone_list):
    """批量异步处理电话号码"""
    tasks = []
    async with aiohttp.ClientSession() as session:
        for phone in phone_list:
            task = process_single_phone(session, phone)
            tasks.append(task)
        results = await asyncio.gather(*tasks)
    return results

# 缓存装饰器
from functools import lru_cache

@lru_cache(maxsize=1000)
def get_area_info(area_code):
    """获取地区信息（带缓存）"""
    return query_area_from_db(area_code)
```

#### 2. API优化
```python
# 分页查询优化
@app.get("/api/phone_records")
async def get_phone_records(
    page: int = 1,
    size: int = 20,
    phone: str = None
):
    offset = (page - 1) * size
    query = select(PhoneRecord).offset(offset).limit(size)
    if phone:
        query = query.where(PhoneRecord.phone_number.like(f"{phone}%"))
    return await db.execute(query)

# 响应压缩
from fastapi.middleware.gzip import GZipMiddleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 请求限流
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter

@app.get("/api/search")
@limiter.limit("100/minute")
async def search_phone(request: Request, phone: str):
    return await search_phone_service(phone)
```

### Frontend服务优化

#### 1. 静态资源优化
```bash
# 压缩CSS/JS文件
npm install -g uglify-js uglifycss

# 压缩JavaScript
uglifyjs app.js -o app.min.js

# 压缩CSS
uglifycss style.css > style.min.css

# 图片优化
# 使用WebP格式
# 实现图片懒加载
# 使用CDN加速
```

#### 2. 前端缓存优化
```javascript
// Service Worker缓存
self.addEventListener('fetch', event => {
    if (event.request.url.includes('/api/')) {
        // API请求缓存策略
        event.respondWith(
            caches.open('api-cache').then(cache => {
                return cache.match(event.request).then(response => {
                    if (response) {
                        // 返回缓存，同时更新缓存
                        fetch(event.request).then(fetchResponse => {
                            cache.put(event.request, fetchResponse.clone());
                        });
                        return response;
                    }
                    return fetch(event.request).then(fetchResponse => {
                        cache.put(event.request, fetchResponse.clone());
                        return fetchResponse;
                    });
                });
            })
        );
    }
});

// 浏览器缓存配置
app.use(express.static('public', {
    maxAge: '1d',
    etag: true,
    lastModified: true
}));
```

## 🔄 微服务性能优化

### 服务间通信优化

#### 1. HTTP连接优化
```python
# 使用连接池
import aiohttp

class ServiceClient:
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        self.session = aiohttp.ClientSession(connector=connector)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()
```

#### 2. 负载均衡优化
```python
# 服务发现和负载均衡
import random
from typing import List

class LoadBalancer:
    def __init__(self, services: List[str]):
        self.services = services
        self.weights = [1] * len(services)
    
    def get_service(self) -> str:
        """加权随机选择服务"""
        return random.choices(self.services, weights=self.weights)[0]
    
    def update_weights(self, service: str, response_time: float):
        """根据响应时间调整权重"""
        index = self.services.index(service)
        # 响应时间越短，权重越高
        self.weights[index] = 1.0 / (response_time + 0.1)
```

### 批处理服务优化

#### 1. 批量处理优化
```python
# 批量处理电话号码
async def batch_process_phones(phones: List[str], batch_size: int = 100):
    """批量处理电话号码"""
    results = []
    for i in range(0, len(phones), batch_size):
        batch = phones[i:i + batch_size]
        batch_results = await process_phone_batch(batch)
        results.extend(batch_results)
        
        # 避免过载，添加延迟
        if i + batch_size < len(phones):
            await asyncio.sleep(0.1)
    
    return results

# 使用队列优化
import asyncio
from asyncio import Queue

class PhoneProcessor:
    def __init__(self, max_workers: int = 10):
        self.queue = Queue()
        self.workers = []
        self.max_workers = max_workers
    
    async def start_workers(self):
        """启动工作进程"""
        for i in range(self.max_workers):
            worker = asyncio.create_task(self.worker(f"worker-{i}"))
            self.workers.append(worker)
    
    async def worker(self, name: str):
        """工作进程"""
        while True:
            phone = await self.queue.get()
            try:
                await self.process_phone(phone)
            except Exception as e:
                logger.error(f"Worker {name} error: {e}")
            finally:
                self.queue.task_done()
```

## 📊 系统监控和调优

### 性能监控

#### 1. 应用性能监控 (APM)
```python
# 添加性能监控装饰器
import time
import functools
from typing import Callable

def monitor_performance(func: Callable):
    """性能监控装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"{func.__name__} executed in {duration:.3f}s")
            
            # 记录到监控系统
            metrics.record_execution_time(func.__name__, duration)
    
    return wrapper

# 使用示例
@monitor_performance
async def search_phone_number(phone: str):
    return await phone_service.search(phone)
```

#### 2. 系统资源监控
```bash
#!/bin/bash
# performance_monitor.sh

# CPU使用率监控
CPU_USAGE=$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
echo "CPU Usage: ${CPU_USAGE}%"

# 内存使用率监控
MEMORY_USAGE=$(vm_stat | grep "Pages active" | awk '{print $3}' | sed 's/\.//')
echo "Memory Usage: ${MEMORY_USAGE}"

# 磁盘I/O监控
DISK_IO=$(iostat -d 1 2 | tail -1 | awk '{print $3}')
echo "Disk I/O: ${DISK_IO}"

# 网络流量监控
NETWORK_IN=$(netstat -ib | grep -E "en0|eth0" | awk '{print $7}')
NETWORK_OUT=$(netstat -ib | grep -E "en0|eth0" | awk '{print $10}')
echo "Network In: ${NETWORK_IN}, Out: ${NETWORK_OUT}"
```

### 自动化调优

#### 1. 自适应配置
```python
# 自适应连接池大小
class AdaptiveConnectionPool:
    def __init__(self, initial_size: int = 10):
        self.pool_size = initial_size
        self.active_connections = 0
        self.wait_time_history = []
    
    def adjust_pool_size(self):
        """根据等待时间调整连接池大小"""
        if len(self.wait_time_history) < 10:
            return
        
        avg_wait_time = sum(self.wait_time_history) / len(self.wait_time_history)
        
        if avg_wait_time > 0.1:  # 等待时间过长，增加连接
            self.pool_size = min(self.pool_size + 2, 50)
        elif avg_wait_time < 0.01:  # 等待时间很短，减少连接
            self.pool_size = max(self.pool_size - 1, 5)
        
        self.wait_time_history.clear()
```

#### 2. 智能缓存策略
```python
# 智能缓存过期时间
class SmartCache:
    def __init__(self):
        self.access_patterns = {}
    
    def get_ttl(self, key: str) -> int:
        """根据访问模式计算TTL"""
        pattern = self.access_patterns.get(key, {})
        access_frequency = pattern.get('frequency', 1)
        last_access = pattern.get('last_access', time.time())
        
        # 访问频率高的数据，TTL更长
        base_ttl = 300  # 5分钟
        frequency_multiplier = min(access_frequency / 10, 5)
        
        return int(base_ttl * frequency_multiplier)
    
    def record_access(self, key: str):
        """记录访问模式"""
        now = time.time()
        if key not in self.access_patterns:
            self.access_patterns[key] = {'frequency': 0, 'last_access': now}
        
        self.access_patterns[key]['frequency'] += 1
        self.access_patterns[key]['last_access'] = now
```

## 🔧 性能调优工具

### 1. 性能分析工具
```bash
# Python性能分析
python3 -m cProfile -o profile.stats your_script.py
python3 -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(10)"

# 内存分析
pip install memory-profiler
python3 -m memory_profiler your_script.py

# 异步性能分析
pip install aiomonitor
# 在代码中添加监控
```

### 2. 数据库性能分析
```sql
-- MySQL性能分析
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看表锁等待
SHOW PROCESSLIST;

-- 查看InnoDB状态
SHOW ENGINE INNODB STATUS;

-- 分析表空间使用
SELECT 
    table_schema,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
ORDER BY (data_length + index_length) DESC;
```

### 3. 系统性能测试
```bash
# API压力测试
# 安装wrk
brew install wrk  # macOS
sudo apt-get install wrk  # Ubuntu

# 执行压力测试
wrk -t12 -c400 -d30s --script=post.lua http://localhost:9099/api/search

# post.lua脚本示例
wrk.method = "POST"
wrk.body   = '{"phone": "13800138000"}'
wrk.headers["Content-Type"] = "application/json"
```

## 📈 性能优化检查清单

### 数据库优化 ✅
- [ ] 创建合适的索引
- [ ] 优化慢查询
- [ ] 配置连接池
- [ ] 启用查询缓存
- [ ] 实现读写分离
- [ ] 配置数据库分区

### 应用服务优化 ✅
- [ ] 实现应用缓存
- [ ] 优化数据库查询
- [ ] 使用异步处理
- [ ] 实现连接池
- [ ] 添加请求限流
- [ ] 优化序列化

### 前端优化 ✅
- [ ] 压缩静态资源
- [ ] 启用浏览器缓存
- [ ] 实现CDN加速
- [ ] 优化图片加载
- [ ] 减少HTTP请求
- [ ] 使用Service Worker

### 系统优化 ✅
- [ ] 调整系统参数
- [ ] 优化网络配置
- [ ] 配置负载均衡
- [ ] 实现服务监控
- [ ] 设置自动扩容
- [ ] 优化日志记录

---

*最后更新：2024年12月*
*版本：v1.0.0*
